package com.example.petdates.data

import com.example.petdates.model.Pet

object SampleData {
    val samplePets = listOf(
        Pet(
            id = "1",
            name = "<PERSON>",
            breed = "Golden Retriever",
            age = 3,
            gender = "Hembra",
            description = "¡Hola! Soy Luna, una golden retriever muy juguetona y cariñosa. Me encanta correr en el parque, nadar y conocer nuevos amigos peludos. Busco una pareja para aventuras y mucho amor. 🐕💕",
            imageUrl = "https://images.unsplash.com/photo-1552053831-71594a27632d?w=400&h=600&fit=crop",
            location = "Madrid, España",
            owner = "<PERSON> García",
            tags = listOf("Juguetona", "Cariñosa", "Activa", "Sociable")
        ),
        Pet(
            id = "2",
            name = "<PERSON>",
            breed = "Labrador",
            age = 2,
            gender = "Macho",
            description = "¡Woof! Soy Max, un labrador lleno de energía. Me fascina jugar a buscar la pelota y dar largos paseos. Soy muy leal y protector. ¿Quieres ser mi compañera de aventuras?",
            imageUrl = "https://images.unsplash.com/photo-1518717758536-85ae29035b6d?w=400&h=600&fit=crop",
            location = "Barcelona, España",
            owner = "<PERSON>",
            tags = listOf("Energético", "Leal", "Protector", "Aventurero")
        ),
        Pet(
            id = "3",
            name = "Bella",
            breed = "Border Collie",
            age = 4,
            gender = "Hembra",
            description = "Soy Bella, una border collie muy inteligente y elegante. Me encanta aprender trucos nuevos y resolver puzzles. Busco un compañero igual de inteligente para compartir juegos mentales.",
            imageUrl = "https://images.unsplash.com/photo-1551717743-49959800b1f6?w=400&h=600&fit=crop",
            location = "Valencia, España",
            owner = "Ana López",
            tags = listOf("Inteligente", "Elegante", "Curiosa", "Entrenada")
        ),
        Pet(
            id = "4",
            name = "Rocky",
            breed = "Bulldog Francés",
            age = 5,
            gender = "Macho",
            description = "¡Hola! Soy Rocky, un bulldog francés con mucha personalidad. Aunque soy pequeño, tengo un gran corazón. Me gusta relajarme en el sofá pero también disfruto de paseos cortos.",
            imageUrl = "https://images.unsplash.com/photo-1583337130417-3346a1be7dee?w=400&h=600&fit=crop",
            location = "Sevilla, España",
            owner = "Pedro Martín",
            tags = listOf("Relajado", "Cariñoso", "Pequeño", "Personalidad")
        ),
        Pet(
            id = "5",
            name = "Coco",
            breed = "Husky Siberiano",
            age = 1,
            gender = "Hembra",
            description = "¡Hola! Soy Coco, una husky muy joven y llena de vida. Me encanta la nieve, correr y aullar a la luna. Busco un compañero que pueda seguir mi ritmo de vida activo.",
            imageUrl = "https://images.unsplash.com/photo-1605568427561-40dd23c2acea?w=400&h=600&fit=crop",
            location = "Bilbao, España",
            owner = "Laura Fernández",
            tags = listOf("Joven", "Activa", "Aventurera", "Independiente")
        ),
        Pet(
            id = "6",
            name = "Toby",
            breed = "Beagle",
            age = 6,
            gender = "Macho",
            description = "Soy Toby, un beagle maduro y sabio. Me encanta olfatear y explorar nuevos lugares. Soy muy tranquilo y busco una compañera que aprecie los placeres simples de la vida.",
            imageUrl = "https://images.unsplash.com/photo-1544717297-fa95b6ee9643?w=400&h=600&fit=crop",
            location = "Zaragoza, España",
            owner = "Miguel Santos",
            tags = listOf("Maduro", "Tranquilo", "Explorador", "Sabio")
        ),
        Pet(
            id = "7",
            name = "Mia",
            breed = "Pastor Alemán",
            age = 3,
            gender = "Hembra",
            description = "¡Hola! Soy Mia, una pastor alemán muy protectora y leal. Me gusta entrenar y mantenerme en forma. Busco un compañero fuerte y confiable como yo.",
            imageUrl = "https://images.unsplash.com/photo-1589941013453-ec89f33b5e95?w=400&h=600&fit=crop",
            location = "Málaga, España",
            owner = "Isabel Ruiz",
            tags = listOf("Protectora", "Leal", "Fuerte", "Entrenada")
        ),
        Pet(
            id = "8",
            name = "Charlie",
            breed = "Cocker Spaniel",
            age = 4,
            gender = "Macho",
            description = "Soy Charlie, un cocker spaniel muy dulce y gentil. Me encanta que me acaricien y dar besos. Soy perfecto para una vida familiar tranquila y llena de amor.",
            imageUrl = "https://images.unsplash.com/photo-1587300003388-59208cc962cb?w=400&h=600&fit=crop",
            location = "Murcia, España",
            owner = "Carmen Jiménez",
            tags = listOf("Dulce", "Gentil", "Familiar", "Cariñoso")
        )
    )
}
