{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/gabrielmordev/portfolio/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { Mail, Phone, Github, Linkedin, ExternalLink, Code, Database, Server, Smartphone } from 'lucide-react';\nimport Image from 'next/image';\n\n// Tech logos component\nconst TechLogo = ({ name, logo }: { name: string; logo: string }) => (\n  <div className=\"flex items-center gap-2 p-2 bg-white/5 rounded-lg border border-white/10 hover:border-purple-400/30 transition-colors\">\n    <div className=\"w-6 h-6 flex items-center justify-center\">\n      <div\n        className=\"w-full h-full bg-contain bg-center bg-no-repeat\"\n        style={{ backgroundImage: `url(${logo})` }}\n      />\n    </div>\n    <span className=\"text-gray-300 text-sm\">{name}</span>\n  </div>\n);\n\nexport default function Home() {\n  const fadeInUp = {\n    initial: { opacity: 0, y: 60 },\n    animate: { opacity: 1, y: 0 },\n    transition: { duration: 0.6 }\n  };\n\n  const staggerContainer = {\n    animate: {\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n\n  // Tech stacks with logos\n  const frontendTechs = [\n    { name: 'React', logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/react/react-original.svg' },\n    { name: 'Vue.js', logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/vuejs/vuejs-original.svg' },\n    { name: 'JavaScript', logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/javascript/javascript-original.svg' },\n    { name: 'TypeScript', logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/typescript/typescript-original.svg' },\n    { name: 'HTML5', logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/html5/html5-original.svg' },\n    { name: 'CSS3', logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/css3/css3-original.svg' }\n  ];\n\n  const backendTechs = [\n    { name: 'Java', logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/java/java-original.svg' },\n    { name: 'Spring', logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/spring/spring-original.svg' },\n    { name: 'Node.js', logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/nodejs/nodejs-original.svg' },\n    { name: 'PHP', logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/php/php-original.svg' },\n    { name: 'Lua', logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/lua/lua-original.svg' }\n  ];\n\n  const databaseTechs = [\n    { name: 'MySQL', logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/mysql/mysql-original.svg' },\n    { name: 'PostgreSQL', logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/postgresql/postgresql-original.svg' }\n  ];\n\n  const toolsTechs = [\n    { name: 'Git', logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/git/git-original.svg' },\n    { name: 'GitHub', logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/github/github-original.svg' },\n    { name: 'WordPress', logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/wordpress/wordpress-original.svg' },\n    { name: 'React Native', logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/react/react-original.svg' }\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900\">\n      {/* Navigation */}\n      <nav className=\"fixed top-0 w-full bg-black/20 backdrop-blur-md z-50 border-b border-white/10\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-4\">\n            <motion.div\n              initial={{ opacity: 0, x: -20 }}\n              animate={{ opacity: 1, x: 0 }}\n              className=\"text-2xl font-bold text-white\"\n            >\n              Gabriel Moreno\n            </motion.div>\n            <motion.div\n              initial={{ opacity: 0, x: 20 }}\n              animate={{ opacity: 1, x: 0 }}\n              className=\"hidden md:flex space-x-8\"\n            >\n              {['Inicio', 'Sobre mí', 'Habilidades', 'Proyectos', 'Experiencia', 'Contacto'].map((item) => (\n                <a\n                  key={item}\n                  href={`#${item.toLowerCase().replace(' ', '-')}`}\n                  className=\"text-gray-300 hover:text-white transition-colors duration-200\"\n                >\n                  {item}\n                </a>\n              ))}\n            </motion.div>\n          </div>\n        </div>\n      </nav>\n\n      {/* Hero Section */}\n      <section id=\"inicio\" className=\"pt-20 pb-20 px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-7xl mx-auto\">\n          <motion.div\n            variants={staggerContainer}\n            initial=\"initial\"\n            animate=\"animate\"\n            className=\"text-center\"\n          >\n            <motion.div\n              variants={fadeInUp}\n              className=\"mb-8\"\n            >\n              <div className=\"w-32 h-32 mx-auto mb-6 rounded-full bg-gradient-to-r from-purple-400 to-pink-400 p-1\">\n                <div className=\"w-full h-full rounded-full bg-gray-800 flex items-center justify-center overflow-hidden\">\n                  <Image\n                    src=\"/profile.png\"\n                    alt=\"Gabriel Moreno\"\n                    width={120}\n                    height={120}\n                    className=\"w-full h-full object-cover rounded-full\"\n                    onError={(e) => {\n                      // Fallback to initials if image fails to load\n                      e.currentTarget.style.display = 'none';\n                      const nextElement = e.currentTarget.nextElementSibling as HTMLElement;\n                      if (nextElement) {\n                        nextElement.style.display = 'flex';\n                      }\n                    }}\n                  />\n                  <span className=\"text-4xl font-bold text-white hidden\">GM</span>\n                </div>\n              </div>\n            </motion.div>\n\n            <motion.h1\n              variants={fadeInUp}\n              className=\"text-5xl md:text-7xl font-bold text-white mb-6\"\n            >\n              Gabriel Moreno\n            </motion.h1>\n\n            <motion.p\n              variants={fadeInUp}\n              className=\"text-xl md:text-2xl text-purple-300 mb-8\"\n            >\n              Desarrollador Fullstack\n            </motion.p>\n\n            <motion.p\n              variants={fadeInUp}\n              className=\"text-lg text-gray-300 max-w-3xl mx-auto mb-12 leading-relaxed text-center\"\n            >\n              Apasionado por la programación y en pleno proceso de formación y crecimiento.\n              Gran motivación por aprender, mejorar y aportar valor desde el primer día.\n            </motion.p>\n\n            <motion.div\n              variants={fadeInUp}\n              className=\"flex flex-col sm:flex-row gap-6 justify-center items-center\"\n            >\n              <div className=\"flex items-center gap-2 text-gray-300\">\n                <Phone className=\"w-5 h-5\" />\n                <span>+34 634668535</span>\n              </div>\n              <div className=\"flex items-center gap-2 text-gray-300\">\n                <Mail className=\"w-5 h-5\" />\n                <span><EMAIL></span>\n              </div>\n            </motion.div>\n\n            <motion.div\n              variants={fadeInUp}\n              className=\"mt-8\"\n            >\n              <a\n                href=\"/Moreno Muñoz, Gabriel - Curriculum.pdf\"\n                download=\"Moreno Muñoz, Gabriel - Curriculum.pdf\"\n                className=\"inline-flex items-center gap-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white font-semibold py-4 px-8 rounded-lg hover:from-purple-600 hover:to-pink-600 transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl\"\n              >\n                <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                </svg>\n                Descargar CV\n              </a>\n            </motion.div>\n          </motion.div>\n        </div>\n      </section>\n\n      {/* About Section */}\n      <section id=\"sobre-mí\" className=\"py-20 px-4 sm:px-6 lg:px-8 bg-black/20\">\n        <div className=\"max-w-7xl mx-auto\">\n          <motion.div\n            initial={{ opacity: 0, y: 60 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"text-center mb-16\"\n          >\n            <h2 className=\"text-4xl md:text-5xl font-bold text-white mb-6\">Sobre mí</h2>\n            <div className=\"w-24 h-1 bg-gradient-to-r from-purple-400 to-pink-400 mx-auto\"></div>\n          </motion.div>\n\n          <div className=\"grid md:grid-cols-2 gap-12 items-center\">\n            <motion.div\n              initial={{ opacity: 0, x: -60 }}\n              whileInView={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.6 }}\n              viewport={{ once: true }}\n            >\n              <h3 className=\"text-2xl font-bold text-white mb-6\">Mi Perfil Profesional</h3>\n              <p className=\"text-gray-300 mb-6 leading-relaxed text-justify\">\n                Desarrollador Fullstack con experiencia en Java, Spring, SQL, React, React Native y WordPress.\n                Me especializo en crear soluciones tecnológicas completas, desde el backend hasta la interfaz de usuario.\n              </p>\n              <p className=\"text-gray-300 mb-6 leading-relaxed text-justify\">\n                Soy una persona <strong className=\"text-purple-300\">proactiva</strong> debido a que anticipo problemas,\n                tomo la iniciativa para resolverlos y busco mejorar continuamente, <strong className=\"text-purple-300\">optimista </strong>\n                 ya que ante posibles problemas nunca decaigo y <strong className=\"text-purple-300\">disciplinado</strong> por mi constancia.\n              </p>\n\n              <div className=\"space-y-4\">\n                <div className=\"flex items-center gap-3\">\n                  <div className=\"w-2 h-2 bg-purple-400 rounded-full\"></div>\n                  <span className=\"text-gray-300\">Esfuerzo y compromiso en cada proyecto</span>\n                </div>\n                <div className=\"flex items-center gap-3\">\n                  <div className=\"w-2 h-2 bg-purple-400 rounded-full\"></div>\n                  <span className=\"text-gray-300\">Mentalidad abierta para nuevas tecnologías</span>\n                </div>\n                <div className=\"flex items-center gap-3\">\n                  <div className=\"w-2 h-2 bg-purple-400 rounded-full\"></div>\n                  <span className=\"text-gray-300\">Determinación para superar desafíos</span>\n                </div>\n              </div>\n            </motion.div>\n\n            <motion.div\n              initial={{ opacity: 0, x: 60 }}\n              whileInView={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.6 }}\n              viewport={{ once: true }}\n              className=\"space-y-6\"\n            >\n              <div className=\"bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10\">\n                <h4 className=\"text-xl font-semibold text-white mb-4\">Educación</h4>\n                <div className=\"space-y-4\">\n                  <div>\n                    <h5 className=\"text-purple-300 font-medium\">Desarrollo de Aplicaciones Multiplataforma</h5>\n                    <p className=\"text-gray-400\">IES GRAN CAPITÁN • 2022-2024</p>\n                  </div>\n                  <div>\n                    <h5 className=\"text-purple-300 font-medium\">Técnico en Sistemas Microinformáticos y Redes</h5>\n                    <p className=\"text-gray-400\">IES FIDIANA • 2020-2022</p>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10\">\n                <h4 className=\"text-xl font-semibold text-white mb-4\">Idiomas</h4>\n                <div className=\"space-y-2\">\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-gray-300\">Español</span>\n                    <span className=\"text-purple-300\">Nativo</span>\n                  </div>\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-gray-300\">Inglés</span>\n                    <span className=\"text-purple-300\">B1</span>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10\">\n                <h4 className=\"text-xl font-semibold text-white mb-4\">Programa Talento Joven</h4>\n                <p className=\"text-gray-300 text-sm text-justify\">\n                  Mi contratación está sujeta a una ayuda de 4.950 € por parte de la Cámara de Comercio de Córdoba,\n                  por mi participación en su programa de cualificación y empleo 'Talento Joven'.\n                </p>\n              </div>\n            </motion.div>\n          </div>\n        </div>\n      </section>\n\n      {/* Skills Section */}\n      <section id=\"habilidades\" className=\"py-20 px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-7xl mx-auto\">\n          <motion.div\n            initial={{ opacity: 0, y: 60 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"text-center mb-16\"\n          >\n            <h2 className=\"text-4xl md:text-5xl font-bold text-white mb-6\">Habilidades Técnicas</h2>\n            <div className=\"w-24 h-1 bg-gradient-to-r from-purple-400 to-pink-400 mx-auto\"></div>\n          </motion.div>\n\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-8\">\n            {/* Frontend */}\n            <motion.div\n              initial={{ opacity: 0, y: 60 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.1 }}\n              viewport={{ once: true }}\n              className=\"bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10 hover:border-purple-400/50 transition-colors\"\n            >\n              <div className=\"flex items-center gap-3 mb-4\">\n                <Code className=\"w-8 h-8 text-purple-400\" />\n                <h3 className=\"text-xl font-semibold text-white\">Frontend</h3>\n              </div>\n              <div className=\"grid grid-cols-2 gap-2\">\n                {frontendTechs.map((tech) => (\n                  <TechLogo key={tech.name} name={tech.name} logo={tech.logo} />\n                ))}\n              </div>\n            </motion.div>\n\n            {/* Backend */}\n            <motion.div\n              initial={{ opacity: 0, y: 60 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.2 }}\n              viewport={{ once: true }}\n              className=\"bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10 hover:border-purple-400/50 transition-colors\"\n            >\n              <div className=\"flex items-center gap-3 mb-4\">\n                <Server className=\"w-8 h-8 text-purple-400\" />\n                <h3 className=\"text-xl font-semibold text-white\">Backend</h3>\n              </div>\n              <div className=\"grid grid-cols-2 gap-2\">\n                {backendTechs.map((tech) => (\n                  <TechLogo key={tech.name} name={tech.name} logo={tech.logo} />\n                ))}\n              </div>\n            </motion.div>\n\n            {/* Database */}\n            <motion.div\n              initial={{ opacity: 0, y: 60 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.3 }}\n              viewport={{ once: true }}\n              className=\"bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10 hover:border-purple-400/50 transition-colors\"\n            >\n              <div className=\"flex items-center gap-3 mb-4\">\n                <Database className=\"w-8 h-8 text-purple-400\" />\n                <h3 className=\"text-xl font-semibold text-white\">Base de Datos</h3>\n              </div>\n              <div className=\"grid grid-cols-2 gap-2\">\n\n                {databaseTechs.map((tech) => (\n                  <TechLogo key={tech.name} name={tech.name} logo={tech.logo} />\n                ))}\n              </div>\n            </motion.div>\n\n            {/* Tools & Others */}\n            <motion.div\n              initial={{ opacity: 0, y: 60 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.4 }}\n              viewport={{ once: true }}\n              className=\"bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10 hover:border-purple-400/50 transition-colors\"\n            >\n              <div className=\"flex items-center gap-3 mb-4\">\n                <Smartphone className=\"w-8 h-8 text-purple-400\" />\n                <h3 className=\"text-xl font-semibold text-white\">Herramientas</h3>\n              </div>\n              <div className=\"grid grid-cols-2 gap-2\">\n                {toolsTechs.map((tech) => (\n                  <TechLogo key={tech.name} name={tech.name} logo={tech.logo} />\n                ))}\n\n              </div>\n            </motion.div>\n          </div>\n        </div>\n      </section>\n\n      {/* Projects Section */}\n      <section id=\"proyectos\" className=\"py-20 px-4 sm:px-6 lg:px-8 bg-black/20\">\n        <div className=\"max-w-7xl mx-auto\">\n          <motion.div\n            initial={{ opacity: 0, y: 60 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"text-center mb-16\"\n          >\n            <h2 className=\"text-4xl md:text-5xl font-bold text-white mb-6\">Proyectos Destacados</h2>\n            <div className=\"w-24 h-1 bg-gradient-to-r from-purple-400 to-pink-400 mx-auto\"></div>\n          </motion.div>\n\n          <div className=\"grid lg:grid-cols-1 gap-8\">\n            <motion.div\n              initial={{ opacity: 0, y: 60 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6 }}\n              viewport={{ once: true }}\n              className=\"bg-white/5 backdrop-blur-sm rounded-lg p-8 border border-white/10 hover:border-purple-400/50 transition-all duration-300 hover:transform hover:scale-105\"\n            >\n              <div className=\"flex flex-col lg:flex-row gap-8\">\n                <div className=\"lg:w-2/3\">\n                  <div className=\"flex items-center gap-3 mb-4\">\n                    <ExternalLink className=\"w-6 h-6 text-purple-400\" />\n                    <h3 className=\"text-2xl font-bold text-white\">Servidor GTA V Roleplay (FiveM)</h3>\n                    <span className=\"text-sm bg-purple-500/20 text-purple-300 px-3 py-1 rounded-full\">2024-2025</span>\n                  </div>\n\n                  <p className=\"text-gray-300 mb-6 leading-relaxed text-justify\">\n                    Desarrollo completo de un servidor multijugador personalizado con arquitectura cliente-servidor, centrado en lógica de juego, sistemas persistentes y rendimiento en tiempo real.                  </p>\n\n                  <div className=\"mb-6\">\n                    <h4 className=\"text-lg font-semibold text-white mb-3\">Características principales:</h4>\n                    <ul className=\"space-y-2 text-gray-300\">\n                      <li className=\"flex items-center gap-2\">\n                        <div className=\"w-2 h-2 bg-purple-400 rounded-full\"></div>\n                        Programación de lógica backend en Lua y JavaScript sobre QB-Core (FiveM).\n                      </li>\n                      <li className=\"flex items-center gap-2\">\n                        <div className=\"w-2 h-2 bg-purple-400 rounded-full\"></div>\n                        Modelado y gestión de base de datos MySQL/MariaDB\n                      </li>\n                      <li className=\"flex items-center gap-2\">\n                        <div className=\"w-2 h-2 bg-purple-400 rounded-full\"></div>\n                        Desarrollo de scripts persistentes: economía, inventario, crafting, permisos,...\n                      </li>\n                      <li className=\"flex items-center gap-2\">\n                        <div className=\"w-2 h-2 bg-purple-400 rounded-full\"></div>\n                        Comunicación cliente-servidor asincrónica (eventos, callbacks, sincronización).\n                      </li>\n                      <li className=\"flex items-center gap-2\">\n                        <div className=\"w-2 h-2 bg-purple-400 rounded-full\"></div>\n                        Control de versiones y colaboración con GitHub.\n                      </li>\n                    </ul>\n                  </div>\n                </div>\n\n                <div className=\"lg:w-1/3\">\n                  <div className=\"bg-white/5 rounded-lg p-6 border border-white/10\">\n                    <h4 className=\"text-lg font-semibold text-white mb-4\">Tecnologías Utilizadas</h4>\n                    <div className=\"space-y-3\">\n                      <div>\n                        <span className=\"text-purple-300 font-medium\">Lenguajes:</span>\n                        <p className=\"text-gray-300 text-sm\">Lua, JavaScript, SQL, HTML/CSS</p>\n                      </div>\n                      <div>\n                        <span className=\"text-purple-300 font-medium\">Frameworks:</span>\n                        <p className=\"text-gray-300 text-sm\">React, Vue.js, QB-Core</p>\n                      </div>\n                      <div>\n                        <span className=\"text-purple-300 font-medium\">Herramientas:</span>\n                        <p className=\"text-gray-300 text-sm\">GitHub, FXServer, VPS</p>\n                      </div>\n                      <div>\n                        <span className=\"text-purple-300 font-medium\">Infraestructura:</span>\n                        <p className=\"text-gray-300 text-sm\">VPS, MySQL</p>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </motion.div>\n          </div>\n        </div>\n      </section>\n\n      {/* Experience Section */}\n      <section id=\"experiencia\" className=\"py-20 px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-7xl mx-auto\">\n          <motion.div\n            initial={{ opacity: 0, y: 60 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"text-center mb-16\"\n          >\n            <h2 className=\"text-4xl md:text-5xl font-bold text-white mb-6\">Experiencia Laboral</h2>\n            <div className=\"w-24 h-1 bg-gradient-to-r from-purple-400 to-pink-400 mx-auto\"></div>\n          </motion.div>\n\n          <div className=\"space-y-8\">\n            {/* AICOR Experience */}\n            <motion.div\n              initial={{ opacity: 0, x: -60 }}\n              whileInView={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.6 }}\n              viewport={{ once: true }}\n              className=\"bg-white/5 backdrop-blur-sm rounded-lg p-8 border border-white/10 hover:border-purple-400/50 transition-colors\"\n            >\n              <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6\">\n                <div>\n                  <h3 className=\"text-2xl font-bold text-white mb-2\">Desarrollador Multiplataforma (FCT)</h3>\n                  <h4 className=\"text-xl text-purple-300 mb-2\">AICOR Consultores Informáticos</h4>\n                  <span className=\"text-gray-400\">2024</span>\n                </div>\n                <div className=\"mt-4 lg:mt-0\">\n                  <span className=\"bg-green-500/20 text-green-300 px-3 py-1 rounded-full text-sm\">\n                    Formación en Centros de Trabajo\n                  </span>\n                </div>\n              </div>\n\n              <p className=\"text-gray-300 mb-4 leading-relaxed text-justify\">\n                Desarrollo de aplicaciones multiplataforma utilizando tecnologías modernas.\n                Participé en proyectos reales trabajando con frameworks y herramientas de desarrollo actuales.\n              </p>\n\n              <div className=\"grid md:grid-cols-2 gap-6\">\n                <div>\n                  <h5 className=\"text-lg font-semibold text-white mb-3\">Responsabilidades:</h5>\n                  <ul className=\"space-y-2 text-gray-300\">\n                    <li className=\"flex items-center gap-2\">\n                      <div className=\"w-2 h-2 bg-purple-400 rounded-full\"></div>\n                      Desarrollo con React Native\n                    </li>\n                    <li className=\"flex items-center gap-2\">\n                      <div className=\"w-2 h-2 bg-purple-400 rounded-full\"></div>\n                      Desarrollo frontend con React\n                    </li>\n                    <li className=\"flex items-center gap-2\">\n                      <div className=\"w-2 h-2 bg-purple-400 rounded-full\"></div>\n                      Desarrollo y mantenimiento en WordPress\n                    </li>\n                  </ul>\n                </div>\n                <div>\n                  <h5 className=\"text-lg font-semibold text-white mb-3\">Tecnologías:</h5>\n                  <div className=\"flex flex-wrap gap-2\">\n                    {['React', 'React Native', 'WordPress', 'PHP', 'JavaScript'].map((tech) => (\n                      <span key={tech} className=\"bg-purple-500/20 text-purple-300 px-3 py-1 rounded-full text-sm\">\n                        {tech}\n                      </span>\n                    ))}\n                  </div>\n                </div>\n              </div>\n            </motion.div>\n\n            {/* Ayuntamiento Experience */}\n            <motion.div\n              initial={{ opacity: 0, x: 60 }}\n              whileInView={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.6 }}\n              viewport={{ once: true }}\n              className=\"bg-white/5 backdrop-blur-sm rounded-lg p-8 border border-white/10 hover:border-purple-400/50 transition-colors\"\n            >\n              <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6\">\n                <div>\n                  <h3 className=\"text-2xl font-bold text-white mb-2\">Técnico Auxiliar Informático (FCT)</h3>\n                  <h4 className=\"text-xl text-purple-300 mb-2\">Ayuntamiento de Córdoba</h4>\n                  <span className=\"text-gray-400\">2022</span>\n                </div>\n                <div className=\"mt-4 lg:mt-0\">\n                  <span className=\"bg-green-500/20 text-green-300 px-3 py-1 rounded-full text-sm\">\n                    Formación en Centros de Trabajo\n                  </span>\n                </div>\n              </div>\n\n              <p className=\"text-gray-300 mb-4 leading-relaxed text-justify\">\n                Experiencia en el mantenimiento y soporte técnico de equipos informáticos en el sector público.\n                Trabajo con hardware y sistemas de la administración local.\n              </p>\n\n              <div>\n                <h5 className=\"text-lg font-semibold text-white mb-3\">Responsabilidades:</h5>\n                <ul className=\"space-y-2 text-gray-300\">\n                  <li className=\"flex items-center gap-2\">\n                    <div className=\"w-2 h-2 bg-purple-400 rounded-full\"></div>\n                    Reparación de equipos informáticos\n                  </li>\n                  <li className=\"flex items-center gap-2\">\n                    <div className=\"w-2 h-2 bg-purple-400 rounded-full\"></div>\n                    Mantenimiento preventivo y correctivo\n                  </li>\n                  <li className=\"flex items-center gap-2\">\n                    <div className=\"w-2 h-2 bg-purple-400 rounded-full\"></div>\n                    Clonación y configuración de equipos\n                  </li>\n                  <li className=\"flex items-center gap-2\">\n                    <div className=\"w-2 h-2 bg-purple-400 rounded-full\"></div>\n                    Soporte técnico a usuarios\n                  </li>\n                </ul>\n              </div>\n            </motion.div>\n          </div>\n        </div>\n      </section>\n\n      {/* Contact Section */}\n      <section id=\"contacto\" className=\"py-20 px-4 sm:px-6 lg:px-8 bg-black/20\">\n        <div className=\"max-w-7xl mx-auto\">\n          <motion.div\n            initial={{ opacity: 0, y: 60 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"text-center mb-16\"\n          >\n            <h2 className=\"text-4xl md:text-5xl font-bold text-white mb-6\">Contacto</h2>\n            <div className=\"w-24 h-1 bg-gradient-to-r from-purple-400 to-pink-400 mx-auto mb-6\"></div>\n            <p className=\"text-xl text-gray-300 max-w-3xl mx-auto text-justify\">\n              ¿Tienes un proyecto en mente? ¡Me encantaría escuchar sobre él y ver cómo puedo ayudarte!\n            </p>\n          </motion.div>\n\n          <div className=\"flex justify-center\">\n            <motion.div\n              initial={{ opacity: 0, y: 60 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6 }}\n              viewport={{ once: true }}\n              className=\"max-w-2xl w-full\"\n            >\n              <div className=\"grid md:grid-cols-2 gap-8\">\n              <div className=\"flex items-center gap-4 p-6 bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 hover:border-purple-400/50 transition-colors\">\n                <div className=\"w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center\">\n                  <Mail className=\"w-6 h-6 text-purple-400\" />\n                </div>\n                <div>\n                  <h3 className=\"text-lg font-semibold text-white\">Email</h3>\n                  <a href=\"mailto:<EMAIL>\" className=\"text-purple-300 hover:text-purple-200 transition-colors\">\n                    <EMAIL>\n                  </a>\n                </div>\n              </div>\n\n              <div className=\"flex items-center gap-4 p-6 bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 hover:border-purple-400/50 transition-colors\">\n                <div className=\"w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center\">\n                  <Phone className=\"w-6 h-6 text-purple-400\" />\n                </div>\n                <div>\n                  <h3 className=\"text-lg font-semibold text-white\">Teléfono</h3>\n                  <a href=\"tel:+34634668535\" className=\"text-purple-300 hover:text-purple-200 transition-colors\">\n                    +34 634 668 535\n                  </a>\n                </div>\n              </div>\n\n\n\n              </div>\n\n              <div className=\"flex justify-center gap-4 mt-8\">\n                <a\n                  href=\"https://github.com/gabrielmordev\"\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"w-12 h-12 bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 hover:border-purple-400/50 flex items-center justify-center transition-colors group\"\n                >\n                  <Github className=\"w-6 h-6 text-gray-400 group-hover:text-purple-400 transition-colors\" />\n                </a>\n                <a\n                  href=\"https://www.linkedin.com/in/gabriel-moreno-munoz/\"\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"w-12 h-12 bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 hover:border-purple-400/50 flex items-center justify-center transition-colors group\"\n                >\n                  <Linkedin className=\"w-6 h-6 text-gray-400 group-hover:text-purple-400 transition-colors\" />\n                </a>\n              </div>\n            </motion.div>\n          </div>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"py-8 px-4 sm:px-6 lg:px-8 border-t border-white/10\">\n        <div className=\"max-w-7xl mx-auto text-center\">\n          <p className=\"text-gray-400\">\n            © 2025 Gabriel Moreno. Desarrollado con Next.js y Tailwind CSS.\n          </p>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAMA,uBAAuB;AACvB,MAAM,WAAW,CAAC,EAAE,IAAI,EAAE,IAAI,EAAkC,iBAC9D,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,WAAU;oBACV,OAAO;wBAAE,iBAAiB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;oBAAC;;;;;;;;;;;0BAG7C,6LAAC;gBAAK,WAAU;0BAAyB;;;;;;;;;;;;KARvC;AAYS,SAAS;IACtB,MAAM,WAAW;QACf,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;IAC9B;IAEA,MAAM,mBAAmB;QACvB,SAAS;YACP,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,yBAAyB;IACzB,MAAM,gBAAgB;QACpB;YAAE,MAAM;YAAS,MAAM;QAA8E;QACrG;YAAE,MAAM;YAAU,MAAM;QAA8E;QACtG;YAAE,MAAM;YAAc,MAAM;QAAwF;QACpH;YAAE,MAAM;YAAc,MAAM;QAAwF;QACpH;YAAE,MAAM;YAAS,MAAM;QAA8E;QACrG;YAAE,MAAM;YAAQ,MAAM;QAA4E;KACnG;IAED,MAAM,eAAe;QACnB;YAAE,MAAM;YAAQ,MAAM;QAA4E;QAClG;YAAE,MAAM;YAAU,MAAM;QAAgF;QACxG;YAAE,MAAM;YAAW,MAAM;QAAgF;QACzG;YAAE,MAAM;YAAO,MAAM;QAA0E;QAC/F;YAAE,MAAM;YAAO,MAAM;QAA0E;KAChG;IAED,MAAM,gBAAgB;QACpB;YAAE,MAAM;YAAS,MAAM;QAA8E;QACrG;YAAE,MAAM;YAAc,MAAM;QAAwF;KACrH;IAED,MAAM,aAAa;QACjB;YAAE,MAAM;YAAO,MAAM;QAA0E;QAC/F;YAAE,MAAM;YAAU,MAAM;QAAgF;QACxG;YAAE,MAAM;YAAa,MAAM;QAAsF;QACjH;YAAE,MAAM;YAAgB,MAAM;QAA8E;KAC7G;IAED,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC9B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,WAAU;0CACX;;;;;;0CAGD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,WAAU;0CAET;oCAAC;oCAAU;oCAAY;oCAAe;oCAAa;oCAAe;iCAAW,CAAC,GAAG,CAAC,CAAC,qBAClF,6LAAC;wCAEC,MAAM,CAAC,CAAC,EAAE,KAAK,WAAW,GAAG,OAAO,CAAC,KAAK,MAAM;wCAChD,WAAU;kDAET;uCAJI;;;;;;;;;;;;;;;;;;;;;;;;;;0BAajB,6LAAC;gBAAQ,IAAG;gBAAS,WAAU;0BAC7B,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,UAAU;wBACV,SAAQ;wBACR,SAAQ;wBACR,WAAU;;0CAEV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,UAAU;gCACV,WAAU;0CAEV,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,gIAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,OAAO;gDACP,QAAQ;gDACR,WAAU;gDACV,SAAS,CAAC;oDACR,8CAA8C;oDAC9C,EAAE,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG;oDAChC,MAAM,cAAc,EAAE,aAAa,CAAC,kBAAkB;oDACtD,IAAI,aAAa;wDACf,YAAY,KAAK,CAAC,OAAO,GAAG;oDAC9B;gDACF;;;;;;0DAEF,6LAAC;gDAAK,WAAU;0DAAuC;;;;;;;;;;;;;;;;;;;;;;0CAK7D,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;gCACR,UAAU;gCACV,WAAU;0CACX;;;;;;0CAID,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;gCACP,UAAU;gCACV,WAAU;0CACX;;;;;;0CAID,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;gCACP,UAAU;gCACV,WAAU;0CACX;;;;;;0CAKD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,UAAU;gCACV,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;0DAAK;;;;;;;;;;;;kDAER,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC;0DAAK;;;;;;;;;;;;;;;;;;0CAIV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,UAAU;gCACV,WAAU;0CAEV,cAAA,6LAAC;oCACC,MAAK;oCACL,UAAS;oCACT,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;wCACjE;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAShB,6LAAC;gBAAQ,IAAG;gBAAW,WAAU;0BAC/B,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,6LAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,6LAAC;oCAAI,WAAU;;;;;;;;;;;;sCAGjB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,UAAU;wCAAE,MAAM;oCAAK;;sDAEvB,6LAAC;4CAAG,WAAU;sDAAqC;;;;;;sDACnD,6LAAC;4CAAE,WAAU;sDAAkD;;;;;;sDAI/D,6LAAC;4CAAE,WAAU;;gDAAkD;8DAC7C,6LAAC;oDAAO,WAAU;8DAAkB;;;;;;gDAAkB;8DACH,6LAAC;oDAAO,WAAU;8DAAkB;;;;;;gDAAmB;8DAC1E,6LAAC;oDAAO,WAAU;8DAAkB;;;;;;gDAAqB;;;;;;;sDAG3G,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;8DAElC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;8DAElC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;;;;;;;8CAKtC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAwC;;;;;;8DACtD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAA8B;;;;;;8EAC5C,6LAAC;oEAAE,WAAU;8EAAgB;;;;;;;;;;;;sEAE/B,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAA8B;;;;;;8EAC5C,6LAAC;oEAAE,WAAU;8EAAgB;;;;;;;;;;;;;;;;;;;;;;;;sDAKnC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAwC;;;;;;8DACtD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,6LAAC;oEAAK,WAAU;8EAAkB;;;;;;;;;;;;sEAEpC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,6LAAC;oEAAK,WAAU;8EAAkB;;;;;;;;;;;;;;;;;;;;;;;;sDAKxC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAwC;;;;;;8DACtD,6LAAC;oDAAE,WAAU;8DAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAW5D,6LAAC;gBAAQ,IAAG;gBAAc,WAAU;0BAClC,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,6LAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,6LAAC;oCAAI,WAAU;;;;;;;;;;;;sCAGjB,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC;oDAAG,WAAU;8DAAmC;;;;;;;;;;;;sDAEnD,6LAAC;4CAAI,WAAU;sDACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,6LAAC;oDAAyB,MAAM,KAAK,IAAI;oDAAE,MAAM,KAAK,IAAI;mDAA3C,KAAK,IAAI;;;;;;;;;;;;;;;;8CAM9B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;oDAAG,WAAU;8DAAmC;;;;;;;;;;;;sDAEnD,6LAAC;4CAAI,WAAU;sDACZ,aAAa,GAAG,CAAC,CAAC,qBACjB,6LAAC;oDAAyB,MAAM,KAAK,IAAI;oDAAE,MAAM,KAAK,IAAI;mDAA3C,KAAK,IAAI;;;;;;;;;;;;;;;;8CAM9B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6LAAC;oDAAG,WAAU;8DAAmC;;;;;;;;;;;;sDAEnD,6LAAC;4CAAI,WAAU;sDAEZ,cAAc,GAAG,CAAC,CAAC,qBAClB,6LAAC;oDAAyB,MAAM,KAAK,IAAI;oDAAE,MAAM,KAAK,IAAI;mDAA3C,KAAK,IAAI;;;;;;;;;;;;;;;;8CAM9B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,iNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,6LAAC;oDAAG,WAAU;8DAAmC;;;;;;;;;;;;sDAEnD,6LAAC;4CAAI,WAAU;sDACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;oDAAyB,MAAM,KAAK,IAAI;oDAAE,MAAM,KAAK,IAAI;mDAA3C,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUpC,6LAAC;gBAAQ,IAAG;gBAAY,WAAU;0BAChC,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,6LAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,6LAAC;oCAAI,WAAU;;;;;;;;;;;;sCAGjB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;0CAEV,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,yNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;sEACxB,6LAAC;4DAAG,WAAU;sEAAgC;;;;;;sEAC9C,6LAAC;4DAAK,WAAU;sEAAkE;;;;;;;;;;;;8DAGpF,6LAAC;oDAAE,WAAU;8DAAkD;;;;;;8DAG/D,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAAwC;;;;;;sEACtD,6LAAC;4DAAG,WAAU;;8EACZ,6LAAC;oEAAG,WAAU;;sFACZ,6LAAC;4EAAI,WAAU;;;;;;wEAA2C;;;;;;;8EAG5D,6LAAC;oEAAG,WAAU;;sFACZ,6LAAC;4EAAI,WAAU;;;;;;wEAA2C;;;;;;;8EAG5D,6LAAC;oEAAG,WAAU;;sFACZ,6LAAC;4EAAI,WAAU;;;;;;wEAA2C;;;;;;;8EAG5D,6LAAC;oEAAG,WAAU;;sFACZ,6LAAC;4EAAI,WAAU;;;;;;wEAA2C;;;;;;;8EAG5D,6LAAC;oEAAG,WAAU;;sFACZ,6LAAC;4EAAI,WAAU;;;;;;wEAA2C;;;;;;;;;;;;;;;;;;;;;;;;;sDAOlE,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAwC;;;;;;kEACtD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC;wEAAK,WAAU;kFAA8B;;;;;;kFAC9C,6LAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;0EAEvC,6LAAC;;kFACC,6LAAC;wEAAK,WAAU;kFAA8B;;;;;;kFAC9C,6LAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;0EAEvC,6LAAC;;kFACC,6LAAC;wEAAK,WAAU;kFAA8B;;;;;;kFAC9C,6LAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;0EAEvC,6LAAC;;kFACC,6LAAC;wEAAK,WAAU;kFAA8B;;;;;;kFAC9C,6LAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAYvD,6LAAC;gBAAQ,IAAG;gBAAc,WAAU;0BAClC,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,6LAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,6LAAC;oCAAI,WAAU;;;;;;;;;;;;sCAGjB,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAAqC;;;;;;sEACnD,6LAAC;4DAAG,WAAU;sEAA+B;;;;;;sEAC7C,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;8DAElC,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEAAgE;;;;;;;;;;;;;;;;;sDAMpF,6LAAC;4CAAE,WAAU;sDAAkD;;;;;;sDAK/D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAAwC;;;;;;sEACtD,6LAAC;4DAAG,WAAU;;8EACZ,6LAAC;oEAAG,WAAU;;sFACZ,6LAAC;4EAAI,WAAU;;;;;;wEAA2C;;;;;;;8EAG5D,6LAAC;oEAAG,WAAU;;sFACZ,6LAAC;4EAAI,WAAU;;;;;;wEAA2C;;;;;;;8EAG5D,6LAAC;oEAAG,WAAU;;sFACZ,6LAAC;4EAAI,WAAU;;;;;;wEAA2C;;;;;;;;;;;;;;;;;;;8DAKhE,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAAwC;;;;;;sEACtD,6LAAC;4DAAI,WAAU;sEACZ;gEAAC;gEAAS;gEAAgB;gEAAa;gEAAO;6DAAa,CAAC,GAAG,CAAC,CAAC,qBAChE,6LAAC;oEAAgB,WAAU;8EACxB;mEADQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAUrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAAqC;;;;;;sEACnD,6LAAC;4DAAG,WAAU;sEAA+B;;;;;;sEAC7C,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;8DAElC,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEAAgE;;;;;;;;;;;;;;;;;sDAMpF,6LAAC;4CAAE,WAAU;sDAAkD;;;;;;sDAK/D,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAwC;;;;;;8DACtD,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC;4DAAG,WAAU;;8EACZ,6LAAC;oEAAI,WAAU;;;;;;gEAA2C;;;;;;;sEAG5D,6LAAC;4DAAG,WAAU;;8EACZ,6LAAC;oEAAI,WAAU;;;;;;gEAA2C;;;;;;;sEAG5D,6LAAC;4DAAG,WAAU;;8EACZ,6LAAC;oEAAI,WAAU;;;;;;gEAA2C;;;;;;;sEAG5D,6LAAC;4DAAG,WAAU;;8EACZ,6LAAC;oEAAI,WAAU;;;;;;gEAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWxE,6LAAC;gBAAQ,IAAG;gBAAW,WAAU;0BAC/B,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,6LAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAE,WAAU;8CAAuD;;;;;;;;;;;;sCAKtE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;;0DACf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;kEAElB,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAmC;;;;;;0EACjD,6LAAC;gEAAE,MAAK;gEAAiC,WAAU;0EAA0D;;;;;;;;;;;;;;;;;;0DAMjH,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;;;;;;kEAEnB,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAmC;;;;;;0EACjD,6LAAC;gEAAE,MAAK;gEAAmB,WAAU;0EAA0D;;;;;;;;;;;;;;;;;;;;;;;;kDAUnG,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,QAAO;gDACP,KAAI;gDACJ,WAAU;0DAEV,cAAA,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;0DAEpB,6LAAC;gDACC,MAAK;gDACL,QAAO;gDACP,KAAI;gDACJ,WAAU;0DAEV,cAAA,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAShC,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;;;;;;AAOvC;MAlpBwB", "debugId": null}}]}