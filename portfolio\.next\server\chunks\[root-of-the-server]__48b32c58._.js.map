{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/gabrielmordev/portfolio/src/app/favicon--route-entry.js"], "sourcesContent": ["import { NextResponse } from 'next/server'\n\nconst contentType = \"image/x-icon\"\nconst cacheControl = \"public, max-age=0, must-revalidate\"\nconst buffer = Buffer.from(\"AAABAAEAEBAAAAEAIABoBAAAFgAAACgAAAAQAAAAIAAAAAEAIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD//////v7+//z8/P/09PT/8fHx//b29v/7/Pz//f39//79/f/8/Pz/9/f3//Hy8v/09PT//Pz8//7+/v///////v7+//v7+//z8/P/9fX1//z7+//79/f/9e3r//Hm5P/k3OD/3djf//Tv7v/7+/v/9vb2//Pz8//7+/v//v7+//z8/P/z8/P/9/f3//v5+f/v5OP/7d3a/+va1//u3tr/xb3L/8K2vv/Vwr7/49fV//r39//3+Pj/8/Pz//z8/P/19fX/9fX1//n29v/n2NX/5NHP/+nY1v/s39//2M7a/7Kvyv+UmLH/vK+x/8u4tv/Owb//9PLy//b29v/19fX/8fHx//v7+//k2NX/0r+4/9nFwP/h0c//0cna/8jA2f+4s9H/eYar/6iiqf/Dsaz/tqei/8/Hxf/6+vr/8vLy//X29v/28vH/1cO+/9K/uP/UwLn/0L+7/8C4zv++udf/trPW/4uUwP+loa7/ybWw/7+uqf+8rqr/7+zr//b29v/6+/v/7OPh/9G9uP/Sv7n/1MG6/9TBvP+9tc7/ubbd/6mp2P96hcT/f4qv/8W0sf/Esq3/u6uo/9/Z1//7+/v//Pz9/+bb2P/Rvbf/0r+5/9TAuv/EuMf/sa7W/7Oy4P+lqN//d4XB/2t9sf+9r7H/xrSv/76tqf/Z0M7//Pz8//z8/P/n3Nn/0Ly3/9K+uP/Ovbv/rarT/6ur1/+hoM7/o6PV/3SBuf9icaP/ua2w/8i1sP/Ar6v/29LQ//z8/P/6+vr/7eXj/9C8t//QvLf/zru3/7Gpv/+lo83/dnmk/4OHtP9+hbT/UVt//7Gkpv/JtbD/wrCt/+Xe3f/6+/v/9PX1//f08//Vw7//z7u2/8Cuqv91dYj/oaDJ/6Siyv+dn8v/lZvN/2x5pf+xpaj/yLSw/8q6t//08fH/9fX1//Ly8v/7+/v/593b/8+7tv+5p6T/MzU//1VcgP+Fibn/a3Sl/1tmmP9TX4b/qp2f/8m2sv/i2Nb/+/v7//Ly8v/39/f/9PT0//v5+f/g09D/yrWy/3pwc/8oLT7/GB80/w0SIf8LEB3/QkNN/7qpqP/d0c7/+vj4//T09P/39/f//f39//X19f/19fX/+/r5/+fe3P/Qvrz/mY2Q/11YXv9EQkf/aGNn/6+kpf/l3Nr/+vn5//b29v/19fX//f39//7+/v/9/f3/9fX1//T09P/7+/v/+PX1/+/o5//l3Nv/4NjY/+3m5f/49vX/+/v7//T09P/19fX//Pz8//7+/v///////v7+//39/f/39/f/8vLy//T09P/4+fn/+/v7//v8/P/5+fn/9PT0//Ly8v/39/f//f39//7+/v//////AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==\", 'base64')\n\nif (false || false) {\n    const fileSizeInMB = buffer.byteLength / 1024 / 1024\n    if (fileSizeInMB > 8) {\n        throw new Error('File size for Open Graph image \"[project]/src/app/favicon.ico\" exceeds 8MB. ' +\n        `(Current: ${fileSizeInMB.toFixed(2)}MB)\\n` +\n        'Read more: https://nextjs.org/docs/app/api-reference/file-conventions/metadata/opengraph-image#image-files-jpg-png-gif'\n        )\n    }\n}\n\nexport function GET() {\n    return new NextResponse(buffer, {\n        headers: {\n            'Content-Type': contentType,\n            'Cache-Control': cacheControl,\n        },\n    })\n}\n\nexport const dynamic = 'force-static'\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,cAAc;AACpB,MAAM,eAAe;AACrB,MAAM,SAAS,OAAO,IAAI,CAAC,ogDAAogD;AAE/hD,uCAAoB;;AAQpB;AAEO,SAAS;IACZ,OAAO,IAAI,8HAAA,CAAA,eAAY,CAAC,QAAQ;QAC5B,SAAS;YACL,gBAAgB;YAChB,iBAAiB;QACrB;IACJ;AACJ;AAEO,MAAM,UAAU", "debugId": null}}]}