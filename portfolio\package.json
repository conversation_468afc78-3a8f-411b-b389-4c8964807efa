{"name": "portfolio", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "rm -rf .next/cache && next build", "start": "next start", "lint": "next lint", "pages:build": "rm -rf .next && next build", "pages:dev": "next dev"}, "dependencies": {"framer-motion": "^12.18.1", "lucide-react": "^0.516.0", "next": "15.3.3", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "typescript": "^5"}}