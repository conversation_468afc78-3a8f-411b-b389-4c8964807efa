# 🌟 Portfolio Personal - <PERSON>

<div align="center">
  <img src="https://img.shields.io/badge/Next.js-000000?style=for-the-badge&logo=next.js&logoColor=white" alt="Next.js" />
  <img src="https://img.shields.io/badge/React-20232A?style=for-the-badge&logo=react&logoColor=61DAFB" alt="React" />
  <img src="https://img.shields.io/badge/TypeScript-007ACC?style=for-the-badge&logo=typescript&logoColor=white" alt="TypeScript" />
  <img src="https://img.shields.io/badge/Tailwind_CSS-38B2AC?style=for-the-badge&logo=tailwind-css&logoColor=white" alt="Tailwind CSS" />
  <img src="https://img.shields.io/badge/Framer_Motion-black?style=for-the-badge&logo=framer&logoColor=blue" alt="Framer Motion" />
</div>

<div align="center">
  <h3>Portfolio profesional moderno y responsivo para desarrollador fullstack</h3>
  <p>Desarrollado con Next.js 15, React 18, TypeScript y Tailwind CSS</p>
</div>

---

## 📋 Descripción del Proyecto

Este es mi portfolio personal desarrollado como una aplicación web moderna que presenta mi experiencia, habilidades y proyectos como desarrollador fullstack. El diseño se centra en una experiencia de usuario fluida con animaciones elegantes y un diseño responsivo.

### 🎯 Objetivos del Portfolio
- Mostrar mis habilidades técnicas y experiencia profesional
- Presentar mis proyectos de manera atractiva y profesional
- Proporcionar una forma fácil de contacto para oportunidades laborales
- Demostrar mis capacidades en desarrollo frontend moderno

---

## ✨ Características Principales

### 🎨 **Diseño y UX**
- **Diseño moderno** con gradientes púrpura y efectos glassmorphism
- **Completamente responsivo** para todos los dispositivos
- **Animaciones fluidas** con Framer Motion
- **Navegación suave** entre secciones
- **Tema oscuro** profesional y elegante

### 📱 **Secciones Incluidas**
1. **Hero Section** - Presentación principal con información de contacto
2. **Sobre mí** - Perfil profesional, educación e idiomas
3. **Habilidades Técnicas** - Stack tecnológico con logos oficiales
4. **Proyectos Destacados** - Servidor GTA V Roleplay con detalles técnicos
5. **Experiencia Laboral** - Historial profesional detallado
6. **Contacto** - Información de contacto y redes sociales

### 🚀 **Funcionalidades**
- **Descarga de CV** - Botón prominente para descargar currículum
- **Logos de tecnologías** - Iconos oficiales de todas las tecnologías
- **Texto justificado** - Formato profesional en todos los párrafos
- **Optimización de imágenes** - Carga optimizada con Next.js Image
- **SEO optimizado** - Meta tags y estructura semántica

---

## 🛠️ Stack Tecnológico

### **Frontend Framework**
- **Next.js 15** - Framework React con App Router
- **React 18** - Biblioteca de interfaz de usuario
- **TypeScript** - Tipado estático para JavaScript

### **Estilos y Animaciones**
- **Tailwind CSS** - Framework de CSS utility-first
- **Framer Motion** - Biblioteca de animaciones para React
- **CSS personalizado** - Estilos adicionales y scrollbar personalizada

### **Iconos y Assets**
- **Lucide React** - Iconos modernos y consistentes
- **DevIcons CDN** - Logos oficiales de tecnologías
- **Google Fonts** - Tipografía Inter para mejor legibilidad

---

## 📁 Estructura del Proyecto

```
portfolio/
├── public/                     # Archivos estáticos
│   ├── profile.jpg            # Imagen de perfil (añadir)
│   ├── cv-gabriel-moreno.pdf  # Currículum (añadir)
│   └── ...
├── src/
│   └── app/
│       ├── globals.css        # Estilos globales
│       ├── layout.tsx         # Layout principal
│       └── page.tsx           # Página principal del portfolio
├── README-CV.md               # Instrucciones para añadir CV
├── README-PROFILE-IMAGE.md    # Instrucciones para añadir imagen
└── README.md                  # Este archivo
```

---

## 🚀 Instalación y Configuración

### **Prerrequisitos**
- Node.js 18+ 
- npm, yarn o pnpm

### **Instalación**
```bash
# Clonar el repositorio
git clone https://github.com/gabrielmordev/portfolio.git

# Navegar al directorio
cd portfolio

# Instalar dependencias
npm install

# Ejecutar en modo desarrollo
npm run dev
```

### **Scripts Disponibles**
```bash
npm run dev      # Servidor de desarrollo
npm run build    # Construir para producción
npm run start    # Servidor de producción
npm run lint     # Linter de código
```

---

## 📝 Configuración Personalizada

### **1. Añadir Imagen de Perfil**
- Coloca tu imagen como `public/profile.jpg`
- Formato recomendado: JPG/PNG, 400x400px, <1MB
- Ver `README-PROFILE-IMAGE.md` para más detalles

### **2. Añadir Currículum**
- Coloca tu CV como `public/cv-gabriel-moreno.pdf`
- Formato: PDF, <5MB
- Ver `README-CV.md` para más detalles

### **3. Personalizar Contenido**
Edita `src/app/page.tsx` para modificar:
- Información personal
- Experiencia laboral
- Proyectos
- Habilidades técnicas
- Enlaces de contacto

---

## 🎨 Personalización de Estilos

### **Colores Principales**
```css
/* Gradiente principal */
from-slate-900 via-purple-900 to-slate-900

/* Acentos */
purple-400, purple-500, pink-400, pink-500

/* Texto */
text-white, text-gray-300, text-purple-300
```

### **Tipografía**
- **Fuente principal:** Inter (Google Fonts)
- **Tamaños responsivos:** text-lg a text-7xl
- **Espaciado:** leading-relaxed para mejor legibilidad

---

## 📱 Responsividad

El portfolio está optimizado para:
- **Desktop:** 1920px+
- **Laptop:** 1024px - 1919px
- **Tablet:** 768px - 1023px
- **Mobile:** 320px - 767px

### **Breakpoints de Tailwind**
- `sm:` 640px+
- `md:` 768px+
- `lg:` 1024px+
- `xl:` 1280px+

---

## 🚀 Despliegue

### **Vercel (Recomendado)**
```bash
# Instalar Vercel CLI
npm i -g vercel

# Desplegar
vercel
```

### **Netlify**
1. Conectar repositorio de GitHub
2. Configurar build: `npm run build`
3. Directorio de salida: `.next`

### **Otras Plataformas**
- GitHub Pages
- Railway
- Render
- DigitalOcean App Platform

---

## 🔧 Optimizaciones Implementadas

### **Rendimiento**
- **Lazy loading** de imágenes con Next.js Image
- **Code splitting** automático con Next.js
- **Optimización de fuentes** con next/font
- **Compresión automática** de assets

### **SEO**
- **Meta tags** optimizados
- **Estructura semántica** HTML5
- **Open Graph** tags para redes sociales
- **Sitemap** automático

### **Accesibilidad**
- **Contraste adecuado** en todos los elementos
- **Navegación por teclado** funcional
- **Alt text** en todas las imágenes
- **Estructura de headings** correcta

---

## 📄 Licencia

Este proyecto está bajo la Licencia MIT. Ver el archivo `LICENSE` para más detalles.

---

## 👨‍💻 Autor

**Gabriel Moreno**
- 📧 Email: [<EMAIL>](mailto:<EMAIL>)
- 📱 Teléfono: [+34 634 668 535](tel:+34634668535)
- 💼 LinkedIn: [linkedin.com/in/gabrielmordev](https://linkedin.com/in/gabrielmordev)
- 🐙 GitHub: [github.com/gabrielmordev](https://github.com/gabrielmordev)

---

## 🤝 Contribuciones

Las contribuciones son bienvenidas. Para cambios importantes:

1. Fork el proyecto
2. Crea una rama para tu feature (`git checkout -b feature/AmazingFeature`)
3. Commit tus cambios (`git commit -m 'Add some AmazingFeature'`)
4. Push a la rama (`git push origin feature/AmazingFeature`)
5. Abre un Pull Request

---

<div align="center">
  <p><strong>⭐ Si te gusta este proyecto, ¡dale una estrella! ⭐</strong></p>
  <p>Desarrollado con ❤️ por Gabriel Moreno</p>
</div>
