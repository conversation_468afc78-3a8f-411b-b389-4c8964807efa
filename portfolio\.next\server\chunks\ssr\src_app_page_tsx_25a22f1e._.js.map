{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/gabrielmordev/portfolio/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { Mail, Phone, MapPin, Github, Linkedin, ExternalLink, Code, Database, Server, Smartphone } from 'lucide-react';\n\nexport default function Home() {\n  const fadeInUp = {\n    initial: { opacity: 0, y: 60 },\n    animate: { opacity: 1, y: 0 },\n    transition: { duration: 0.6 }\n  };\n\n  const staggerContainer = {\n    animate: {\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900\">\n      {/* Navigation */}\n      <nav className=\"fixed top-0 w-full bg-black/20 backdrop-blur-md z-50 border-b border-white/10\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-4\">\n            <motion.div\n              initial={{ opacity: 0, x: -20 }}\n              animate={{ opacity: 1, x: 0 }}\n              className=\"text-2xl font-bold text-white\"\n            >\n              <PERSON>\n            </motion.div>\n            <motion.div\n              initial={{ opacity: 0, x: 20 }}\n              animate={{ opacity: 1, x: 0 }}\n              className=\"hidden md:flex space-x-8\"\n            >\n              {['Inicio', 'Sobre mí', 'Habilidades', 'Proyectos', 'Experiencia', 'Contacto'].map((item) => (\n                <a\n                  key={item}\n                  href={`#${item.toLowerCase().replace(' ', '-')}`}\n                  className=\"text-gray-300 hover:text-white transition-colors duration-200\"\n                >\n                  {item}\n                </a>\n              ))}\n            </motion.div>\n          </div>\n        </div>\n      </nav>\n\n      {/* Hero Section */}\n      <section id=\"inicio\" className=\"pt-20 pb-20 px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-7xl mx-auto\">\n          <motion.div\n            variants={staggerContainer}\n            initial=\"initial\"\n            animate=\"animate\"\n            className=\"text-center\"\n          >\n            <motion.div\n              variants={fadeInUp}\n              className=\"mb-8\"\n            >\n              <div className=\"w-32 h-32 mx-auto mb-6 rounded-full bg-gradient-to-r from-purple-400 to-pink-400 p-1\">\n                <div className=\"w-full h-full rounded-full bg-gray-800 flex items-center justify-center\">\n                  <span className=\"text-4xl font-bold text-white\">GM</span>\n                </div>\n              </div>\n            </motion.div>\n\n            <motion.h1\n              variants={fadeInUp}\n              className=\"text-5xl md:text-7xl font-bold text-white mb-6\"\n            >\n              Gabriel Moreno\n            </motion.h1>\n\n            <motion.p\n              variants={fadeInUp}\n              className=\"text-xl md:text-2xl text-purple-300 mb-8\"\n            >\n              Desarrollador Fullstack\n            </motion.p>\n\n            <motion.p\n              variants={fadeInUp}\n              className=\"text-lg text-gray-300 max-w-3xl mx-auto mb-12 leading-relaxed\"\n            >\n              Desarrollador Fullstack con experiencia en Java, Spring, SQL, React, React Native, Laravel y WordPress.\n              Soy una persona proactiva, optimista y disciplinada, siempre buscando mejorar continuamente.\n            </motion.p>\n\n            <motion.div\n              variants={fadeInUp}\n              className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\"\n            >\n              <div className=\"flex items-center gap-2 text-gray-300\">\n                <Phone className=\"w-5 h-5\" />\n                <span>+34 634668535</span>\n              </div>\n              <div className=\"flex items-center gap-2 text-gray-300\">\n                <Mail className=\"w-5 h-5\" />\n                <span><EMAIL></span>\n              </div>\n              <div className=\"flex items-center gap-2 text-gray-300\">\n                <MapPin className=\"w-5 h-5\" />\n                <span>Córdoba, España</span>\n              </div>\n            </motion.div>\n          </motion.div>\n        </div>\n      </section>\n\n      {/* About Section */}\n      <section id=\"sobre-mí\" className=\"py-20 px-4 sm:px-6 lg:px-8 bg-black/20\">\n        <div className=\"max-w-7xl mx-auto\">\n          <motion.div\n            initial={{ opacity: 0, y: 60 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"text-center mb-16\"\n          >\n            <h2 className=\"text-4xl md:text-5xl font-bold text-white mb-6\">Sobre mí</h2>\n            <div className=\"w-24 h-1 bg-gradient-to-r from-purple-400 to-pink-400 mx-auto\"></div>\n          </motion.div>\n\n          <div className=\"grid md:grid-cols-2 gap-12 items-center\">\n            <motion.div\n              initial={{ opacity: 0, x: -60 }}\n              whileInView={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.6 }}\n              viewport={{ once: true }}\n            >\n              <h3 className=\"text-2xl font-bold text-white mb-6\">Mi Perfil Profesional</h3>\n              <p className=\"text-gray-300 mb-6 leading-relaxed\">\n                Desarrollador Fullstack con experiencia en Java, Spring, SQL, React, React Native, Laravel y WordPress.\n                Me especializo en crear soluciones tecnológicas completas, desde el backend hasta la interfaz de usuario.\n              </p>\n              <p className=\"text-gray-300 mb-6 leading-relaxed\">\n                Soy una persona <strong className=\"text-purple-300\">proactiva</strong> debido a que anticipo problemas,\n                tomo la iniciativa para resolverlos y busco mejorar continuamente, <strong className=\"text-purple-300\">optimista</strong>\n                ya que ante posibles problemas nunca decaigo y <strong className=\"text-purple-300\">disciplinado</strong> por mi constancia.\n              </p>\n\n              <div className=\"space-y-4\">\n                <div className=\"flex items-center gap-3\">\n                  <div className=\"w-2 h-2 bg-purple-400 rounded-full\"></div>\n                  <span className=\"text-gray-300\">Esfuerzo y compromiso en cada proyecto</span>\n                </div>\n                <div className=\"flex items-center gap-3\">\n                  <div className=\"w-2 h-2 bg-purple-400 rounded-full\"></div>\n                  <span className=\"text-gray-300\">Mentalidad abierta para nuevas tecnologías</span>\n                </div>\n                <div className=\"flex items-center gap-3\">\n                  <div className=\"w-2 h-2 bg-purple-400 rounded-full\"></div>\n                  <span className=\"text-gray-300\">Determinación para superar desafíos</span>\n                </div>\n              </div>\n            </motion.div>\n\n            <motion.div\n              initial={{ opacity: 0, x: 60 }}\n              whileInView={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.6 }}\n              viewport={{ once: true }}\n              className=\"space-y-6\"\n            >\n              <div className=\"bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10\">\n                <h4 className=\"text-xl font-semibold text-white mb-4\">Educación</h4>\n                <div className=\"space-y-4\">\n                  <div>\n                    <h5 className=\"text-purple-300 font-medium\">Desarrollo de Aplicaciones Multiplataforma</h5>\n                    <p className=\"text-gray-400\">IES GRAN CAPITÁN • 2022-2024</p>\n                  </div>\n                  <div>\n                    <h5 className=\"text-purple-300 font-medium\">Técnico en Sistemas Microinformáticos y Redes</h5>\n                    <p className=\"text-gray-400\">IES FIDIANA • 2020-2022</p>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10\">\n                <h4 className=\"text-xl font-semibold text-white mb-4\">Idiomas</h4>\n                <div className=\"space-y-2\">\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-gray-300\">Español</span>\n                    <span className=\"text-purple-300\">Nativo</span>\n                  </div>\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-gray-300\">Inglés</span>\n                    <span className=\"text-purple-300\">B1</span>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10\">\n                <h4 className=\"text-xl font-semibold text-white mb-4\">Programa Talento Joven</h4>\n                <p className=\"text-gray-300 text-sm\">\n                  Mi contratación está sujeta a una ayuda de 4.950 € por parte de la Cámara de Comercio de Córdoba,\n                  por mi participación en su programa de cualificación y empleo 'Talento Joven'.\n                </p>\n              </div>\n            </motion.div>\n          </div>\n        </div>\n      </section>\n\n      {/* Skills Section */}\n      <section id=\"habilidades\" className=\"py-20 px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-7xl mx-auto\">\n          <motion.div\n            initial={{ opacity: 0, y: 60 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"text-center mb-16\"\n          >\n            <h2 className=\"text-4xl md:text-5xl font-bold text-white mb-6\">Habilidades Técnicas</h2>\n            <div className=\"w-24 h-1 bg-gradient-to-r from-purple-400 to-pink-400 mx-auto\"></div>\n          </motion.div>\n\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-8\">\n            {/* Frontend */}\n            <motion.div\n              initial={{ opacity: 0, y: 60 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.1 }}\n              viewport={{ once: true }}\n              className=\"bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10 hover:border-purple-400/50 transition-colors\"\n            >\n              <div className=\"flex items-center gap-3 mb-4\">\n                <Code className=\"w-8 h-8 text-purple-400\" />\n                <h3 className=\"text-xl font-semibold text-white\">Frontend</h3>\n              </div>\n              <ul className=\"space-y-2 text-gray-300\">\n                <li>• React</li>\n                <li>• React Native</li>\n                <li>• Vue.js</li>\n                <li>• HTML/CSS</li>\n                <li>• JavaScript</li>\n                <li>• TypeScript</li>\n              </ul>\n            </motion.div>\n\n            {/* Backend */}\n            <motion.div\n              initial={{ opacity: 0, y: 60 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.2 }}\n              viewport={{ once: true }}\n              className=\"bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10 hover:border-purple-400/50 transition-colors\"\n            >\n              <div className=\"flex items-center gap-3 mb-4\">\n                <Server className=\"w-8 h-8 text-purple-400\" />\n                <h3 className=\"text-xl font-semibold text-white\">Backend</h3>\n              </div>\n              <ul className=\"space-y-2 text-gray-300\">\n                <li>• Java</li>\n                <li>• Spring Framework</li>\n                <li>• Laravel</li>\n                <li>• Node.js</li>\n                <li>• PHP</li>\n                <li>• Lua</li>\n              </ul>\n            </motion.div>\n\n            {/* Database */}\n            <motion.div\n              initial={{ opacity: 0, y: 60 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.3 }}\n              viewport={{ once: true }}\n              className=\"bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10 hover:border-purple-400/50 transition-colors\"\n            >\n              <div className=\"flex items-center gap-3 mb-4\">\n                <Database className=\"w-8 h-8 text-purple-400\" />\n                <h3 className=\"text-xl font-semibold text-white\">Base de Datos</h3>\n              </div>\n              <ul className=\"space-y-2 text-gray-300\">\n                <li>• SQL</li>\n                <li>• MySQL</li>\n                <li>• PostgreSQL</li>\n                <li>• MongoDB</li>\n              </ul>\n            </motion.div>\n\n            {/* Tools & Others */}\n            <motion.div\n              initial={{ opacity: 0, y: 60 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.4 }}\n              viewport={{ once: true }}\n              className=\"bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10 hover:border-purple-400/50 transition-colors\"\n            >\n              <div className=\"flex items-center gap-3 mb-4\">\n                <Smartphone className=\"w-8 h-8 text-purple-400\" />\n                <h3 className=\"text-xl font-semibold text-white\">Herramientas</h3>\n              </div>\n              <ul className=\"space-y-2 text-gray-300\">\n                <li>• Git/GitHub</li>\n                <li>• WordPress</li>\n                <li>• FXServer</li>\n                <li>• QB-Core</li>\n                <li>• VPS</li>\n              </ul>\n            </motion.div>\n          </div>\n        </div>\n      </section>\n\n      {/* Projects Section */}\n      <section id=\"proyectos\" className=\"py-20 px-4 sm:px-6 lg:px-8 bg-black/20\">\n        <div className=\"max-w-7xl mx-auto\">\n          <motion.div\n            initial={{ opacity: 0, y: 60 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"text-center mb-16\"\n          >\n            <h2 className=\"text-4xl md:text-5xl font-bold text-white mb-6\">Proyectos Destacados</h2>\n            <div className=\"w-24 h-1 bg-gradient-to-r from-purple-400 to-pink-400 mx-auto\"></div>\n          </motion.div>\n\n          <div className=\"grid lg:grid-cols-1 gap-8\">\n            <motion.div\n              initial={{ opacity: 0, y: 60 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6 }}\n              viewport={{ once: true }}\n              className=\"bg-white/5 backdrop-blur-sm rounded-lg p-8 border border-white/10 hover:border-purple-400/50 transition-all duration-300 hover:transform hover:scale-105\"\n            >\n              <div className=\"flex flex-col lg:flex-row gap-8\">\n                <div className=\"lg:w-2/3\">\n                  <div className=\"flex items-center gap-3 mb-4\">\n                    <ExternalLink className=\"w-6 h-6 text-purple-400\" />\n                    <h3 className=\"text-2xl font-bold text-white\">Servidor GTA V Roleplay (FiveM)</h3>\n                    <span className=\"text-sm bg-purple-500/20 text-purple-300 px-3 py-1 rounded-full\">2024-2025</span>\n                  </div>\n\n                  <p className=\"text-gray-300 mb-6 leading-relaxed\">\n                    Desarrollo completo de un servidor GTA V Roleplay utilizando el framework FiveM.\n                    Este proyecto incluye la creación y configuración de sistemas complejos de juego,\n                    interfaces de usuario personalizadas y gestión de base de datos para una experiencia\n                    de roleplay inmersiva.\n                  </p>\n\n                  <div className=\"mb-6\">\n                    <h4 className=\"text-lg font-semibold text-white mb-3\">Características principales:</h4>\n                    <ul className=\"space-y-2 text-gray-300\">\n                      <li className=\"flex items-center gap-2\">\n                        <div className=\"w-2 h-2 bg-purple-400 rounded-full\"></div>\n                        Sistema de economía y trabajos personalizados\n                      </li>\n                      <li className=\"flex items-center gap-2\">\n                        <div className=\"w-2 h-2 bg-purple-400 rounded-full\"></div>\n                        Interfaces de usuario desarrolladas con React y Vue.js\n                      </li>\n                      <li className=\"flex items-center gap-2\">\n                        <div className=\"w-2 h-2 bg-purple-400 rounded-full\"></div>\n                        Sistema de inventario y crafting avanzado\n                      </li>\n                      <li className=\"flex items-center gap-2\">\n                        <div className=\"w-2 h-2 bg-purple-400 rounded-full\"></div>\n                        Gestión de usuarios y permisos\n                      </li>\n                    </ul>\n                  </div>\n                </div>\n\n                <div className=\"lg:w-1/3\">\n                  <div className=\"bg-white/5 rounded-lg p-6 border border-white/10\">\n                    <h4 className=\"text-lg font-semibold text-white mb-4\">Tecnologías Utilizadas</h4>\n                    <div className=\"space-y-3\">\n                      <div>\n                        <span className=\"text-purple-300 font-medium\">Lenguajes:</span>\n                        <p className=\"text-gray-300 text-sm\">Lua, JavaScript, HTML/CSS, SQL</p>\n                      </div>\n                      <div>\n                        <span className=\"text-purple-300 font-medium\">Frameworks:</span>\n                        <p className=\"text-gray-300 text-sm\">React, Vue.js, QB-Core</p>\n                      </div>\n                      <div>\n                        <span className=\"text-purple-300 font-medium\">Herramientas:</span>\n                        <p className=\"text-gray-300 text-sm\">GitHub, FXServer, VPS</p>\n                      </div>\n                      <div>\n                        <span className=\"text-purple-300 font-medium\">Infraestructura:</span>\n                        <p className=\"text-gray-300 text-sm\">Servidor VPS, Base de datos MySQL</p>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </motion.div>\n          </div>\n        </div>\n      </section>\n\n      {/* Experience Section */}\n      <section id=\"experiencia\" className=\"py-20 px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-7xl mx-auto\">\n          <motion.div\n            initial={{ opacity: 0, y: 60 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"text-center mb-16\"\n          >\n            <h2 className=\"text-4xl md:text-5xl font-bold text-white mb-6\">Experiencia Laboral</h2>\n            <div className=\"w-24 h-1 bg-gradient-to-r from-purple-400 to-pink-400 mx-auto\"></div>\n          </motion.div>\n\n          <div className=\"space-y-8\">\n            {/* AICOR Experience */}\n            <motion.div\n              initial={{ opacity: 0, x: -60 }}\n              whileInView={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.6 }}\n              viewport={{ once: true }}\n              className=\"bg-white/5 backdrop-blur-sm rounded-lg p-8 border border-white/10 hover:border-purple-400/50 transition-colors\"\n            >\n              <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6\">\n                <div>\n                  <h3 className=\"text-2xl font-bold text-white mb-2\">Desarrollador Multiplataforma (FCT)</h3>\n                  <h4 className=\"text-xl text-purple-300 mb-2\">AICOR Consultores Informáticos</h4>\n                  <span className=\"text-gray-400\">2024</span>\n                </div>\n                <div className=\"mt-4 lg:mt-0\">\n                  <span className=\"bg-green-500/20 text-green-300 px-3 py-1 rounded-full text-sm\">\n                    Formación en Centros de Trabajo\n                  </span>\n                </div>\n              </div>\n\n              <p className=\"text-gray-300 mb-4 leading-relaxed\">\n                Desarrollo de aplicaciones multiplataforma utilizando tecnologías modernas.\n                Participé en proyectos reales trabajando con frameworks y herramientas de desarrollo actuales.\n              </p>\n\n              <div className=\"grid md:grid-cols-2 gap-6\">\n                <div>\n                  <h5 className=\"text-lg font-semibold text-white mb-3\">Responsabilidades:</h5>\n                  <ul className=\"space-y-2 text-gray-300\">\n                    <li className=\"flex items-center gap-2\">\n                      <div className=\"w-2 h-2 bg-purple-400 rounded-full\"></div>\n                      Desarrollo con React Native\n                    </li>\n                    <li className=\"flex items-center gap-2\">\n                      <div className=\"w-2 h-2 bg-purple-400 rounded-full\"></div>\n                      Desarrollo frontend con React\n                    </li>\n                    <li className=\"flex items-center gap-2\">\n                      <div className=\"w-2 h-2 bg-purple-400 rounded-full\"></div>\n                      Desarrollo backend con Laravel\n                    </li>\n                    <li className=\"flex items-center gap-2\">\n                      <div className=\"w-2 h-2 bg-purple-400 rounded-full\"></div>\n                      Desarrollo y mantenimiento en WordPress\n                    </li>\n                  </ul>\n                </div>\n                <div>\n                  <h5 className=\"text-lg font-semibold text-white mb-3\">Tecnologías:</h5>\n                  <div className=\"flex flex-wrap gap-2\">\n                    {['React', 'React Native', 'Laravel', 'WordPress', 'PHP', 'JavaScript'].map((tech) => (\n                      <span key={tech} className=\"bg-purple-500/20 text-purple-300 px-3 py-1 rounded-full text-sm\">\n                        {tech}\n                      </span>\n                    ))}\n                  </div>\n                </div>\n              </div>\n            </motion.div>\n\n            {/* Ayuntamiento Experience */}\n            <motion.div\n              initial={{ opacity: 0, x: 60 }}\n              whileInView={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.6 }}\n              viewport={{ once: true }}\n              className=\"bg-white/5 backdrop-blur-sm rounded-lg p-8 border border-white/10 hover:border-purple-400/50 transition-colors\"\n            >\n              <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6\">\n                <div>\n                  <h3 className=\"text-2xl font-bold text-white mb-2\">Técnico Auxiliar Informático (FCT)</h3>\n                  <h4 className=\"text-xl text-purple-300 mb-2\">Ayuntamiento de Córdoba</h4>\n                  <span className=\"text-gray-400\">2022</span>\n                </div>\n                <div className=\"mt-4 lg:mt-0\">\n                  <span className=\"bg-blue-500/20 text-blue-300 px-3 py-1 rounded-full text-sm\">\n                    Sector Público\n                  </span>\n                </div>\n              </div>\n\n              <p className=\"text-gray-300 mb-4 leading-relaxed\">\n                Experiencia en el mantenimiento y soporte técnico de equipos informáticos en el sector público.\n                Trabajo con hardware y sistemas de la administración local.\n              </p>\n\n              <div>\n                <h5 className=\"text-lg font-semibold text-white mb-3\">Responsabilidades:</h5>\n                <ul className=\"space-y-2 text-gray-300\">\n                  <li className=\"flex items-center gap-2\">\n                    <div className=\"w-2 h-2 bg-purple-400 rounded-full\"></div>\n                    Reparación de equipos informáticos\n                  </li>\n                  <li className=\"flex items-center gap-2\">\n                    <div className=\"w-2 h-2 bg-purple-400 rounded-full\"></div>\n                    Mantenimiento preventivo y correctivo\n                  </li>\n                  <li className=\"flex items-center gap-2\">\n                    <div className=\"w-2 h-2 bg-purple-400 rounded-full\"></div>\n                    Clonación y configuración de equipos\n                  </li>\n                  <li className=\"flex items-center gap-2\">\n                    <div className=\"w-2 h-2 bg-purple-400 rounded-full\"></div>\n                    Soporte técnico a usuarios\n                  </li>\n                </ul>\n              </div>\n            </motion.div>\n          </div>\n        </div>\n      </section>\n\n      {/* Contact Section */}\n      <section id=\"contacto\" className=\"py-20 px-4 sm:px-6 lg:px-8 bg-black/20\">\n        <div className=\"max-w-7xl mx-auto\">\n          <motion.div\n            initial={{ opacity: 0, y: 60 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"text-center mb-16\"\n          >\n            <h2 className=\"text-4xl md:text-5xl font-bold text-white mb-6\">Contacto</h2>\n            <div className=\"w-24 h-1 bg-gradient-to-r from-purple-400 to-pink-400 mx-auto mb-6\"></div>\n            <p className=\"text-xl text-gray-300 max-w-3xl mx-auto\">\n              ¿Tienes un proyecto en mente? ¡Me encantaría escuchar sobre él y ver cómo puedo ayudarte!\n            </p>\n          </motion.div>\n\n          <div className=\"grid md:grid-cols-2 gap-12 items-center\">\n            <motion.div\n              initial={{ opacity: 0, x: -60 }}\n              whileInView={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.6 }}\n              viewport={{ once: true }}\n              className=\"space-y-8\"\n            >\n              <div className=\"flex items-center gap-4 p-6 bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 hover:border-purple-400/50 transition-colors\">\n                <div className=\"w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center\">\n                  <Mail className=\"w-6 h-6 text-purple-400\" />\n                </div>\n                <div>\n                  <h3 className=\"text-lg font-semibold text-white\">Email</h3>\n                  <a href=\"mailto:<EMAIL>\" className=\"text-purple-300 hover:text-purple-200 transition-colors\">\n                    <EMAIL>\n                  </a>\n                </div>\n              </div>\n\n              <div className=\"flex items-center gap-4 p-6 bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 hover:border-purple-400/50 transition-colors\">\n                <div className=\"w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center\">\n                  <Phone className=\"w-6 h-6 text-purple-400\" />\n                </div>\n                <div>\n                  <h3 className=\"text-lg font-semibold text-white\">Teléfono</h3>\n                  <a href=\"tel:+34634668535\" className=\"text-purple-300 hover:text-purple-200 transition-colors\">\n                    +34 634 668 535\n                  </a>\n                </div>\n              </div>\n\n              <div className=\"flex items-center gap-4 p-6 bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 hover:border-purple-400/50 transition-colors\">\n                <div className=\"w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center\">\n                  <MapPin className=\"w-6 h-6 text-purple-400\" />\n                </div>\n                <div>\n                  <h3 className=\"text-lg font-semibold text-white\">Ubicación</h3>\n                  <p className=\"text-purple-300\">Córdoba, España</p>\n                </div>\n              </div>\n\n              <div className=\"flex gap-4\">\n                <a\n                  href=\"https://github.com/gabrielmordev\"\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"w-12 h-12 bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 hover:border-purple-400/50 flex items-center justify-center transition-colors group\"\n                >\n                  <Github className=\"w-6 h-6 text-gray-400 group-hover:text-purple-400 transition-colors\" />\n                </a>\n                <a\n                  href=\"https://linkedin.com/in/gabrielmordev\"\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"w-12 h-12 bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 hover:border-purple-400/50 flex items-center justify-center transition-colors group\"\n                >\n                  <Linkedin className=\"w-6 h-6 text-gray-400 group-hover:text-purple-400 transition-colors\" />\n                </a>\n              </div>\n            </motion.div>\n\n            <motion.div\n              initial={{ opacity: 0, x: 60 }}\n              whileInView={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.6 }}\n              viewport={{ once: true }}\n              className=\"bg-white/5 backdrop-blur-sm rounded-lg p-8 border border-white/10\"\n            >\n              <h3 className=\"text-2xl font-bold text-white mb-6\">Envíame un mensaje</h3>\n              <form className=\"space-y-6\">\n                <div>\n                  <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-300 mb-2\">\n                    Nombre\n                  </label>\n                  <input\n                    type=\"text\"\n                    id=\"name\"\n                    className=\"w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg text-white placeholder-gray-400 focus:border-purple-400 focus:outline-none transition-colors\"\n                    placeholder=\"Tu nombre\"\n                  />\n                </div>\n                <div>\n                  <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-300 mb-2\">\n                    Email\n                  </label>\n                  <input\n                    type=\"email\"\n                    id=\"email\"\n                    className=\"w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg text-white placeholder-gray-400 focus:border-purple-400 focus:outline-none transition-colors\"\n                    placeholder=\"<EMAIL>\"\n                  />\n                </div>\n                <div>\n                  <label htmlFor=\"message\" className=\"block text-sm font-medium text-gray-300 mb-2\">\n                    Mensaje\n                  </label>\n                  <textarea\n                    id=\"message\"\n                    rows={5}\n                    className=\"w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg text-white placeholder-gray-400 focus:border-purple-400 focus:outline-none transition-colors resize-none\"\n                    placeholder=\"Cuéntame sobre tu proyecto...\"\n                  ></textarea>\n                </div>\n                <button\n                  type=\"submit\"\n                  className=\"w-full bg-gradient-to-r from-purple-500 to-pink-500 text-white font-semibold py-3 px-6 rounded-lg hover:from-purple-600 hover:to-pink-600 transition-all duration-200 transform hover:scale-105\"\n                >\n                  Enviar Mensaje\n                </button>\n              </form>\n            </motion.div>\n          </div>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"py-8 px-4 sm:px-6 lg:px-8 border-t border-white/10\">\n        <div className=\"max-w-7xl mx-auto text-center\">\n          <p className=\"text-gray-400\">\n            © 2024 Gabriel Moreno. Desarrollado con Next.js y Tailwind CSS.\n          </p>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,WAAW;QACf,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;IAC9B;IAEA,MAAM,mBAAmB;QACvB,SAAS;YACP,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC9B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,WAAU;0CACX;;;;;;0CAGD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,WAAU;0CAET;oCAAC;oCAAU;oCAAY;oCAAe;oCAAa;oCAAe;iCAAW,CAAC,GAAG,CAAC,CAAC,qBAClF,8OAAC;wCAEC,MAAM,CAAC,CAAC,EAAE,KAAK,WAAW,GAAG,OAAO,CAAC,KAAK,MAAM;wCAChD,WAAU;kDAET;uCAJI;;;;;;;;;;;;;;;;;;;;;;;;;;0BAajB,8OAAC;gBAAQ,IAAG;gBAAS,WAAU;0BAC7B,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,UAAU;wBACV,SAAQ;wBACR,SAAQ;wBACR,WAAU;;0CAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,UAAU;gCACV,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;;;;0CAKtD,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;gCACR,UAAU;gCACV,WAAU;0CACX;;;;;;0CAID,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gCACP,UAAU;gCACV,WAAU;0CACX;;;;;;0CAID,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gCACP,UAAU;gCACV,WAAU;0CACX;;;;;;0CAKD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,UAAU;gCACV,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhB,8OAAC;gBAAQ,IAAG;gBAAW,WAAU;0BAC/B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,8OAAC;oCAAI,WAAU;;;;;;;;;;;;sCAGjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,UAAU;wCAAE,MAAM;oCAAK;;sDAEvB,8OAAC;4CAAG,WAAU;sDAAqC;;;;;;sDACnD,8OAAC;4CAAE,WAAU;sDAAqC;;;;;;sDAIlD,8OAAC;4CAAE,WAAU;;gDAAqC;8DAChC,8OAAC;oDAAO,WAAU;8DAAkB;;;;;;gDAAkB;8DACH,8OAAC;oDAAO,WAAU;8DAAkB;;;;;;gDAAkB;8DAC1E,8OAAC;oDAAO,WAAU;8DAAkB;;;;;;gDAAqB;;;;;;;sDAG1G,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;8DAElC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;8DAElC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;;;;;;;8CAKtC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAwC;;;;;;8DACtD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EAA8B;;;;;;8EAC5C,8OAAC;oEAAE,WAAU;8EAAgB;;;;;;;;;;;;sEAE/B,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EAA8B;;;;;;8EAC5C,8OAAC;oEAAE,WAAU;8EAAgB;;;;;;;;;;;;;;;;;;;;;;;;sDAKnC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAwC;;;;;;8DACtD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,8OAAC;oEAAK,WAAU;8EAAkB;;;;;;;;;;;;sEAEpC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,8OAAC;oEAAK,WAAU;8EAAkB;;;;;;;;;;;;;;;;;;;;;;;;sDAKxC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAwC;;;;;;8DACtD,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAW/C,8OAAC;gBAAQ,IAAG;gBAAc,WAAU;0BAClC,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,8OAAC;oCAAI,WAAU;;;;;;;;;;;;sCAGjB,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;;;;;;;sDAEnD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;;;;;;;;;;;;;8CAKR,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;;;;;;;sDAEnD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;;;;;;;;;;;;;8CAKR,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;;;;;;;sDAEnD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;;;;;;;;;;;;;8CAKR,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,8MAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;;;;;;;sDAEnD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQd,8OAAC;gBAAQ,IAAG;gBAAY,WAAU;0BAChC,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,8OAAC;oCAAI,WAAU;;;;;;;;;;;;sCAGjB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,sNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;sEACxB,8OAAC;4DAAG,WAAU;sEAAgC;;;;;;sEAC9C,8OAAC;4DAAK,WAAU;sEAAkE;;;;;;;;;;;;8DAGpF,8OAAC;oDAAE,WAAU;8DAAqC;;;;;;8DAOlD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAwC;;;;;;sEACtD,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEAAG,WAAU;;sFACZ,8OAAC;4EAAI,WAAU;;;;;;wEAA2C;;;;;;;8EAG5D,8OAAC;oEAAG,WAAU;;sFACZ,8OAAC;4EAAI,WAAU;;;;;;wEAA2C;;;;;;;8EAG5D,8OAAC;oEAAG,WAAU;;sFACZ,8OAAC;4EAAI,WAAU;;;;;;wEAA2C;;;;;;;8EAG5D,8OAAC;oEAAG,WAAU;;sFACZ,8OAAC;4EAAI,WAAU;;;;;;wEAA2C;;;;;;;;;;;;;;;;;;;;;;;;;sDAOlE,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAwC;;;;;;kEACtD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFACC,8OAAC;wEAAK,WAAU;kFAA8B;;;;;;kFAC9C,8OAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;0EAEvC,8OAAC;;kFACC,8OAAC;wEAAK,WAAU;kFAA8B;;;;;;kFAC9C,8OAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;0EAEvC,8OAAC;;kFACC,8OAAC;wEAAK,WAAU;kFAA8B;;;;;;kFAC9C,8OAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;0EAEvC,8OAAC;;kFACC,8OAAC;wEAAK,WAAU;kFAA8B;;;;;;kFAC9C,8OAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAYvD,8OAAC;gBAAQ,IAAG;gBAAc,WAAU;0BAClC,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,8OAAC;oCAAI,WAAU;;;;;;;;;;;;sCAGjB,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAqC;;;;;;sEACnD,8OAAC;4DAAG,WAAU;sEAA+B;;;;;;sEAC7C,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;8DAElC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAAgE;;;;;;;;;;;;;;;;;sDAMpF,8OAAC;4CAAE,WAAU;sDAAqC;;;;;;sDAKlD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAwC;;;;;;sEACtD,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEAAG,WAAU;;sFACZ,8OAAC;4EAAI,WAAU;;;;;;wEAA2C;;;;;;;8EAG5D,8OAAC;oEAAG,WAAU;;sFACZ,8OAAC;4EAAI,WAAU;;;;;;wEAA2C;;;;;;;8EAG5D,8OAAC;oEAAG,WAAU;;sFACZ,8OAAC;4EAAI,WAAU;;;;;;wEAA2C;;;;;;;8EAG5D,8OAAC;oEAAG,WAAU;;sFACZ,8OAAC;4EAAI,WAAU;;;;;;wEAA2C;;;;;;;;;;;;;;;;;;;8DAKhE,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAwC;;;;;;sEACtD,8OAAC;4DAAI,WAAU;sEACZ;gEAAC;gEAAS;gEAAgB;gEAAW;gEAAa;gEAAO;6DAAa,CAAC,GAAG,CAAC,CAAC,qBAC3E,8OAAC;oEAAgB,WAAU;8EACxB;mEADQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAUrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAqC;;;;;;sEACnD,8OAAC;4DAAG,WAAU;sEAA+B;;;;;;sEAC7C,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;8DAElC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAA8D;;;;;;;;;;;;;;;;;sDAMlF,8OAAC;4CAAE,WAAU;sDAAqC;;;;;;sDAKlD,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAwC;;;;;;8DACtD,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEAAI,WAAU;;;;;;gEAA2C;;;;;;;sEAG5D,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEAAI,WAAU;;;;;;gEAA2C;;;;;;;sEAG5D,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEAAI,WAAU;;;;;;gEAA2C;;;;;;;sEAG5D,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEAAI,WAAU;;;;;;gEAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWxE,8OAAC;gBAAQ,IAAG;gBAAW,WAAU;0BAC/B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAKzD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAElB,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAmC;;;;;;sEACjD,8OAAC;4DAAE,MAAK;4DAAiC,WAAU;sEAA0D;;;;;;;;;;;;;;;;;;sDAMjH,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;8DAEnB,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAmC;;;;;;sEACjD,8OAAC;4DAAE,MAAK;4DAAmB,WAAU;sEAA0D;;;;;;;;;;;;;;;;;;sDAMnG,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;8DAEpB,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAmC;;;;;;sEACjD,8OAAC;4DAAE,WAAU;sEAAkB;;;;;;;;;;;;;;;;;;sDAInC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,MAAK;oDACL,QAAO;oDACP,KAAI;oDACJ,WAAU;8DAEV,cAAA,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;8DAEpB,8OAAC;oDACC,MAAK;oDACL,QAAO;oDACP,KAAI;oDACJ,WAAU;8DAEV,cAAA,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8CAK1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,8OAAC;4CAAG,WAAU;sDAAqC;;;;;;sDACnD,8OAAC;4CAAK,WAAU;;8DACd,8OAAC;;sEACC,8OAAC;4DAAM,SAAQ;4DAAO,WAAU;sEAA+C;;;;;;sEAG/E,8OAAC;4DACC,MAAK;4DACL,IAAG;4DACH,WAAU;4DACV,aAAY;;;;;;;;;;;;8DAGhB,8OAAC;;sEACC,8OAAC;4DAAM,SAAQ;4DAAQ,WAAU;sEAA+C;;;;;;sEAGhF,8OAAC;4DACC,MAAK;4DACL,IAAG;4DACH,WAAU;4DACV,aAAY;;;;;;;;;;;;8DAGhB,8OAAC;;sEACC,8OAAC;4DAAM,SAAQ;4DAAU,WAAU;sEAA+C;;;;;;sEAGlF,8OAAC;4DACC,IAAG;4DACH,MAAM;4DACN,WAAU;4DACV,aAAY;;;;;;;;;;;;8DAGhB,8OAAC;oDACC,MAAK;oDACL,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUX,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;;;;;;AAOvC", "debugId": null}}]}