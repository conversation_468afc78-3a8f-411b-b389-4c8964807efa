package com.example.petdates.navigation

import androidx.compose.runtime.Composable
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.example.petdates.ui.screens.MatchesScreen
import com.example.petdates.ui.screens.PetDetailScreen
import com.example.petdates.ui.screens.SwipeScreen
import com.example.petdates.viewmodel.PetDatesViewModel

sealed class Screen(val route: String) {
    object Swipe : Screen("swipe")
    object Matches : Screen("matches")
    object PetDetail : Screen("pet_detail/{petId}") {
        fun createRoute(petId: String) = "pet_detail/$petId"
    }
}

@Composable
fun PetDatesNavigation(
    navController: NavHostController = rememberNavController(),
    viewModel: PetDatesViewModel = viewModel()
) {
    NavHost(
        navController = navController,
        startDestination = Screen.Swipe.route
    ) {
        composable(Screen.Swipe.route) {
            SwipeScreen(
                onNavigateToMatches = {
                    navController.navigate(Screen.Matches.route)
                },
                viewModel = viewModel
            )
        }
        
        composable(Screen.Matches.route) {
            MatchesScreen(
                onNavigateBack = {
                    navController.popBackStack()
                },
                onPetClick = { petId ->
                    navController.navigate(Screen.PetDetail.createRoute(petId))
                },
                viewModel = viewModel
            )
        }
        
        composable(Screen.PetDetail.route) { backStackEntry ->
            val petId = backStackEntry.arguments?.getString("petId") ?: ""
            PetDetailScreen(
                petId = petId,
                onNavigateBack = {
                    navController.popBackStack()
                },
                viewModel = viewModel
            )
        }
    }
}
