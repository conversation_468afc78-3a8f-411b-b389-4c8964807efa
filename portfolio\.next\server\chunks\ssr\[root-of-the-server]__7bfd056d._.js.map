{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/gabrielmordev/portfolio/src/app/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { motion } from 'framer-motion';\r\nimport { Mail, Phone, Github, Linkedin, ExternalLink, Code, Database, Server, Smartphone } from 'lucide-react';\r\nimport Image from 'next/image';\r\n\r\n// Tech logos component\r\nconst TechLogo = ({ name, logo }: { name: string; logo: string }) => (\r\n  <div className=\"flex items-center gap-2 p-2 bg-white/5 rounded-lg border border-white/10 hover:border-purple-400/30 transition-colors\">\r\n    <div className=\"w-6 h-6 flex items-center justify-center\">\r\n      <div\r\n        className=\"w-full h-full bg-contain bg-center bg-no-repeat\"\r\n        style={{ backgroundImage: `url(${logo})` }}\r\n      />\r\n    </div>\r\n    <span className=\"text-gray-300 text-sm\">{name}</span>\r\n  </div>\r\n);\r\n\r\nexport default function Home() {\r\n  const fadeInUp = {\r\n    initial: { opacity: 0, y: 60 },\r\n    animate: { opacity: 1, y: 0 },\r\n    transition: { duration: 0.6 }\r\n  };\r\n\r\n  const staggerContainer = {\r\n    animate: {\r\n      transition: {\r\n        staggerChildren: 0.1\r\n      }\r\n    }\r\n  };\r\n\r\n  // Tech stacks with logos\r\n  const frontendTechs = [\r\n    { name: 'React', logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/react/react-original.svg' },\r\n    { name: 'Vue.js', logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/vuejs/vuejs-original.svg' },\r\n    { name: 'JavaScript', logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/javascript/javascript-original.svg' },\r\n    { name: 'TypeScript', logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/typescript/typescript-original.svg' },\r\n    { name: 'HTML5', logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/html5/html5-original.svg' },\r\n    { name: 'CSS3', logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/css3/css3-original.svg' }\r\n  ];\r\n\r\n  const backendTechs = [\r\n    { name: 'Java', logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/java/java-original.svg' },\r\n    { name: 'Spring', logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/spring/spring-original.svg' },\r\n    { name: 'Node.js', logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/nodejs/nodejs-original.svg' },\r\n    { name: 'PHP', logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/php/php-original.svg' },\r\n    { name: 'Lua', logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/lua/lua-original.svg' }\r\n  ];\r\n\r\n  const databaseTechs = [\r\n    { name: 'MySQL', logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/mysql/mysql-original.svg' },\r\n    { name: 'MariaDB', logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/mariadb/mariadb-original.svg' }\r\n  ];\r\n\r\n  const toolsTechs = [\r\n    { name: 'Git', logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/git/git-original.svg' },\r\n    { name: 'GitHub', logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/github/github-original.svg' },\r\n    { name: 'WordPress', logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/wordpress/wordpress-original.svg' },\r\n    { name: 'React Native', logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/react/react-original.svg' }\r\n  ];\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900\">\r\n      {/* Navigation */}\r\n      <nav className=\"fixed top-0 w-full bg-black/20 backdrop-blur-md z-50 border-b border-white/10\">\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"flex justify-between items-center py-4\">\r\n            <motion.div\r\n              initial={{ opacity: 0, x: -20 }}\r\n              animate={{ opacity: 1, x: 0 }}\r\n              className=\"text-2xl font-bold text-white\"\r\n            >\r\n              Gabriel Moreno\r\n            </motion.div>\r\n            <motion.div\r\n              initial={{ opacity: 0, x: 20 }}\r\n              animate={{ opacity: 1, x: 0 }}\r\n              className=\"hidden md:flex space-x-8\"\r\n            >\r\n              {['Inicio', 'Sobre mí', 'Habilidades', 'Proyectos', 'Experiencia', 'Contacto'].map((item) => (\r\n                <a\r\n                  key={item}\r\n                  href={`#${item.toLowerCase().replace(' ', '-')}`}\r\n                  className=\"text-gray-300 hover:text-white transition-colors duration-200\"\r\n                >\r\n                  {item}\r\n                </a>\r\n              ))}\r\n            </motion.div>\r\n          </div>\r\n        </div>\r\n      </nav>\r\n\r\n      {/* Hero Section */}\r\n      <section id=\"inicio\" className=\"pt-20 pb-20 px-4 sm:px-6 lg:px-8\">\r\n        <div className=\"max-w-7xl mx-auto\">\r\n          <motion.div\r\n            variants={staggerContainer}\r\n            initial=\"initial\"\r\n            animate=\"animate\"\r\n            className=\"text-center\"\r\n          >\r\n            <motion.div\r\n              variants={fadeInUp}\r\n              className=\"mb-8\"\r\n            >\r\n              <div className=\"w-32 h-32 mx-auto mb-6 rounded-full bg-gradient-to-r from-purple-400 to-pink-400 p-1\">\r\n                <div className=\"w-full h-full rounded-full bg-gray-800 flex items-center justify-center overflow-hidden\">\r\n                  <Image\r\n                    src=\"/profile.png\"\r\n                    alt=\"Gabriel Moreno\"\r\n                    width={120}\r\n                    height={120}\r\n                    className=\"w-full h-full object-cover rounded-full\"\r\n                    onError={(e) => {\r\n                      // Fallback to initials if image fails to load\r\n                      e.currentTarget.style.display = 'none';\r\n                      const nextElement = e.currentTarget.nextElementSibling as HTMLElement;\r\n                      if (nextElement) {\r\n                        nextElement.style.display = 'flex';\r\n                      }\r\n                    }}\r\n                  />\r\n                  <span className=\"text-4xl font-bold text-white hidden\">GM</span>\r\n                </div>\r\n              </div>\r\n            </motion.div>\r\n\r\n            <motion.h1\r\n              variants={fadeInUp}\r\n              className=\"text-5xl md:text-7xl font-bold text-white mb-6\"\r\n            >\r\n              Gabriel Moreno\r\n            </motion.h1>\r\n\r\n            <motion.p\r\n              variants={fadeInUp}\r\n              className=\"text-xl md:text-2xl text-purple-300 mb-8\"\r\n            >\r\n              Desarrollador Fullstack\r\n            </motion.p>\r\n\r\n            <motion.p\r\n              variants={fadeInUp}\r\n              className=\"text-lg text-gray-300 max-w-3xl mx-auto mb-12 leading-relaxed text-center\"\r\n            >\r\n              Apasionado por la programación y en pleno proceso de formación y crecimiento.\r\n              Gran motivación por aprender, mejorar y aportar valor desde el primer día.\r\n            </motion.p>\r\n\r\n            <motion.div\r\n              variants={fadeInUp}\r\n              className=\"flex flex-col sm:flex-row gap-6 justify-center items-center\"\r\n            >\r\n              <div className=\"flex items-center gap-2 text-gray-300\">\r\n                <Phone className=\"w-5 h-5\" />\r\n                <span>+34 634668535</span>\r\n              </div>\r\n              <div className=\"flex items-center gap-2 text-gray-300\">\r\n                <Mail className=\"w-5 h-5\" />\r\n                <span><EMAIL></span>\r\n              </div>\r\n            </motion.div>\r\n\r\n            <motion.div\r\n              variants={fadeInUp}\r\n              className=\"mt-8\"\r\n            >\r\n              <a\r\n                href=\"/Moreno Muñoz, Gabriel - Curriculum.pdf\"\r\n                download=\"Moreno Muñoz, Gabriel - Curriculum.pdf\"\r\n                className=\"inline-flex items-center gap-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white font-semibold py-4 px-8 rounded-lg hover:from-purple-600 hover:to-pink-600 transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl\"\r\n              >\r\n                <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\r\n                </svg>\r\n                Descargar CV\r\n              </a>\r\n            </motion.div>\r\n          </motion.div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* About Section */}\r\n      <section id=\"sobre-mí\" className=\"py-20 px-4 sm:px-6 lg:px-8 bg-black/20\">\r\n        <div className=\"max-w-7xl mx-auto\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 60 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6 }}\r\n            viewport={{ once: true }}\r\n            className=\"text-center mb-16\"\r\n          >\r\n            <h2 className=\"text-4xl md:text-5xl font-bold text-white mb-6\">Sobre mí</h2>\r\n            <div className=\"w-24 h-1 bg-gradient-to-r from-purple-400 to-pink-400 mx-auto\"></div>\r\n          </motion.div>\r\n\r\n          <div className=\"grid md:grid-cols-2 gap-12 items-center\">\r\n            <motion.div\r\n              initial={{ opacity: 0, x: -60 }}\r\n              whileInView={{ opacity: 1, x: 0 }}\r\n              transition={{ duration: 0.6 }}\r\n              viewport={{ once: true }}\r\n            >\r\n              <h3 className=\"text-2xl font-bold text-white mb-6\">Mi Perfil Profesional</h3>\r\n              <p className=\"text-gray-300 mb-6 leading-relaxed text-justify\">\r\n                Apasionado por la programación y en pleno proceso de formación y crecimiento. Gran motivación por aprender, mejorar y aportar valor desde el primer día.\r\n              </p>\r\n              <p className=\"text-gray-300 mb-6 leading-relaxed text-justify\">\r\n                Me esfuerzo por dar siempre lo mejor de mí, soy <strong className=\"text-purple-300\">constante</strong>, <strong className=\"text-purple-300\">responsable </strong> y tengo muchas ganas de empezar a trabajar en un\r\n                entorno profesional donde pueda desarrollarme y contribuir al equipo.\r\n              </p>\r\n\r\n              <div className=\"space-y-4\">\r\n                <div className=\"flex items-center gap-3\">\r\n                  <div className=\"w-2 h-2 bg-purple-400 rounded-full\"></div>\r\n                  <span className=\"text-gray-300\">Actitud proactiva y resolutiva</span>\r\n                </div>\r\n                <div className=\"flex items-center gap-3\">\r\n                  <div className=\"w-2 h-2 bg-purple-400 rounded-full\"></div>\r\n                  <span className=\"text-gray-300\">Gran capacidad de aprendizaje</span>\r\n                </div>\r\n                <div className=\"flex items-center gap-3\">\r\n                  <div className=\"w-2 h-2 bg-purple-400 rounded-full\"></div>\r\n                  <span className=\"text-gray-300\">Adaptabilidad a nuevas herramientas y metodologías</span>\r\n                </div>\r\n                <div className=\"flex items-center gap-3\">\r\n                  <div className=\"w-2 h-2 bg-purple-400 rounded-full\"></div>\r\n                  <span className=\"text-gray-300\">Interés por la mejora continua y el crecimiento profesional</span>\r\n                </div>\r\n\r\n              </div>\r\n            </motion.div>\r\n\r\n            <motion.div\r\n              initial={{ opacity: 0, x: 60 }}\r\n              whileInView={{ opacity: 1, x: 0 }}\r\n              transition={{ duration: 0.6 }}\r\n              viewport={{ once: true }}\r\n              className=\"space-y-6\"\r\n            >\r\n              <div className=\"bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10\">\r\n                <h4 className=\"text-xl font-semibold text-white mb-4\">Educación</h4>\r\n                <div className=\"space-y-4\">\r\n                  <div>\r\n                    <h5 className=\"text-purple-300 font-medium\">Desarrollo de Aplicaciones Multiplataforma</h5>\r\n                    <p className=\"text-gray-400\">IES GRAN CAPITÁN • 2022-2024</p>\r\n                  </div>\r\n                  <div>\r\n                    <h5 className=\"text-purple-300 font-medium\">Técnico en Sistemas Microinformáticos y Redes</h5>\r\n                    <p className=\"text-gray-400\">IES FIDIANA • 2020-2022</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10\">\r\n                <h4 className=\"text-xl font-semibold text-white mb-4\">Idiomas</h4>\r\n                <div className=\"space-y-2\">\r\n                  <div className=\"flex justify-between items-center\">\r\n                    <span className=\"text-gray-300\">Español</span>\r\n                    <span className=\"text-purple-300\">Nativo</span>\r\n                  </div>\r\n                  <div className=\"flex justify-between items-center\">\r\n                    <span className=\"text-gray-300\">Inglés</span>\r\n                    <span className=\"text-purple-300\">B1</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10\">\r\n                <h4 className=\"text-xl font-semibold text-white mb-4\">Programa Talento Joven</h4>\r\n                <p className=\"text-gray-300 text-sm text-justify\">\r\n                  Mi contratación está sujeta a una ayuda de 4.950 € por parte de la Cámara de Comercio de Córdoba,\r\n                  por mi participación en su programa de cualificación y empleo &apos;Talento Joven&apos;.\r\n                </p>\r\n              </div>\r\n            </motion.div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Skills Section */}\r\n      <section id=\"habilidades\" className=\"py-20 px-4 sm:px-6 lg:px-8\">\r\n        <div className=\"max-w-7xl mx-auto\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 60 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6 }}\r\n            viewport={{ once: true }}\r\n            className=\"text-center mb-16\"\r\n          >\r\n            <h2 className=\"text-4xl md:text-5xl font-bold text-white mb-6\">Habilidades Técnicas</h2>\r\n            <div className=\"w-24 h-1 bg-gradient-to-r from-purple-400 to-pink-400 mx-auto\"></div>\r\n          </motion.div>\r\n\r\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-8\">\r\n            {/* Frontend */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 60 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6, delay: 0.1 }}\r\n              viewport={{ once: true }}\r\n              className=\"bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10 hover:border-purple-400/50 transition-colors\"\r\n            >\r\n              <div className=\"flex items-center gap-3 mb-4\">\r\n                <Code className=\"w-8 h-8 text-purple-400\" />\r\n                <h3 className=\"text-xl font-semibold text-white\">Frontend</h3>\r\n              </div>\r\n              <div className=\"grid grid-cols-2 gap-2\">\r\n                {frontendTechs.map((tech) => (\r\n                  <TechLogo key={tech.name} name={tech.name} logo={tech.logo} />\r\n                ))}\r\n              </div>\r\n            </motion.div>\r\n\r\n            {/* Backend */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 60 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6, delay: 0.2 }}\r\n              viewport={{ once: true }}\r\n              className=\"bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10 hover:border-purple-400/50 transition-colors\"\r\n            >\r\n              <div className=\"flex items-center gap-3 mb-4\">\r\n                <Server className=\"w-8 h-8 text-purple-400\" />\r\n                <h3 className=\"text-xl font-semibold text-white\">Backend</h3>\r\n              </div>\r\n              <div className=\"grid grid-cols-2 gap-2\">\r\n                {backendTechs.map((tech) => (\r\n                  <TechLogo key={tech.name} name={tech.name} logo={tech.logo} />\r\n                ))}\r\n              </div>\r\n            </motion.div>\r\n\r\n            {/* Database */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 60 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6, delay: 0.3 }}\r\n              viewport={{ once: true }}\r\n              className=\"bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10 hover:border-purple-400/50 transition-colors\"\r\n            >\r\n              <div className=\"flex items-center gap-3 mb-4\">\r\n                <Database className=\"w-8 h-8 text-purple-400\" />\r\n                <h3 className=\"text-xl font-semibold text-white\">Base de Datos</h3>\r\n              </div>\r\n              <div className=\"grid grid-cols-2 gap-2\">\r\n\r\n                {databaseTechs.map((tech) => (\r\n                  <TechLogo key={tech.name} name={tech.name} logo={tech.logo} />\r\n                ))}\r\n              </div>\r\n            </motion.div>\r\n\r\n            {/* Tools & Others */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 60 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6, delay: 0.4 }}\r\n              viewport={{ once: true }}\r\n              className=\"bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10 hover:border-purple-400/50 transition-colors\"\r\n            >\r\n              <div className=\"flex items-center gap-3 mb-4\">\r\n                <Smartphone className=\"w-8 h-8 text-purple-400\" />\r\n                <h3 className=\"text-xl font-semibold text-white\">Herramientas</h3>\r\n              </div>\r\n              <div className=\"grid grid-cols-2 gap-2\">\r\n                {toolsTechs.map((tech) => (\r\n                  <TechLogo key={tech.name} name={tech.name} logo={tech.logo} />\r\n                ))}\r\n\r\n              </div>\r\n            </motion.div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Projects Section */}\r\n      <section id=\"proyectos\" className=\"py-20 px-4 sm:px-6 lg:px-8 bg-black/20\">\r\n        <div className=\"max-w-7xl mx-auto\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 60 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6 }}\r\n            viewport={{ once: true }}\r\n            className=\"text-center mb-16\"\r\n          >\r\n            <h2 className=\"text-4xl md:text-5xl font-bold text-white mb-6\">Proyectos Destacados</h2>\r\n            <div className=\"w-24 h-1 bg-gradient-to-r from-purple-400 to-pink-400 mx-auto\"></div>\r\n          </motion.div>\r\n\r\n          <div className=\"grid lg:grid-cols-1 gap-8\">\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 60 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6 }}\r\n              viewport={{ once: true }}\r\n              className=\"bg-white/5 backdrop-blur-sm rounded-lg p-8 border border-white/10 hover:border-purple-400/50 transition-all duration-300 hover:transform hover:scale-105\"\r\n            >\r\n              <div className=\"flex flex-col lg:flex-row gap-8\">\r\n                <div className=\"lg:w-2/3\">\r\n                  <div className=\"flex items-center gap-3 mb-4\">\r\n                    <ExternalLink className=\"w-6 h-6 text-purple-400\" />\r\n                    <h3 className=\"text-2xl font-bold text-white\">Servidor GTA V Roleplay (FiveM)</h3>\r\n                    <span className=\"text-sm bg-purple-500/20 text-purple-300 px-3 py-1 rounded-full\">2024-2025</span>\r\n                  </div>\r\n\r\n                  <p className=\"text-gray-300 mb-6 leading-relaxed text-justify\">\r\n                    Desarrollo completo de un servidor multijugador personalizado con arquitectura cliente-servidor, centrado en lógica de juego, sistemas persistentes y rendimiento en tiempo real.                  </p>\r\n\r\n                  <div className=\"mb-6\">\r\n                    <h4 className=\"text-lg font-semibold text-white mb-3\">Características principales:</h4>\r\n                    <ul className=\"space-y-2 text-gray-300\">\r\n                      <li className=\"flex items-center gap-2\">\r\n                        <div className=\"w-2 h-2 bg-purple-400 rounded-full\"></div>\r\n                        Programación de lógica backend en Lua y JavaScript sobre QB-Core (FiveM).\r\n                      </li>\r\n                      <li className=\"flex items-center gap-2\">\r\n                        <div className=\"w-2 h-2 bg-purple-400 rounded-full\"></div>\r\n                        Modelado y gestión de base de datos MySQL/MariaDB\r\n                      </li>\r\n                      <li className=\"flex items-center gap-2\">\r\n                        <div className=\"w-2 h-2 bg-purple-400 rounded-full\"></div>\r\n                        Desarrollo de scripts persistentes: economía, inventario, crafting, permisos,...\r\n                      </li>\r\n                      <li className=\"flex items-center gap-2\">\r\n                        <div className=\"w-2 h-2 bg-purple-400 rounded-full\"></div>\r\n                        Comunicación cliente-servidor asincrónica (eventos, callbacks, sincronización).\r\n                      </li>\r\n                      <li className=\"flex items-center gap-2\">\r\n                        <div className=\"w-2 h-2 bg-purple-400 rounded-full\"></div>\r\n                        Control de versiones y colaboración con GitHub.\r\n                      </li>\r\n                    </ul>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"lg:w-1/3\">\r\n                  <div className=\"bg-white/5 rounded-lg p-6 border border-white/10\">\r\n                    <h4 className=\"text-lg font-semibold text-white mb-4\">Tecnologías Utilizadas</h4>\r\n                    <div className=\"space-y-3\">\r\n                      <div>\r\n                        <span className=\"text-purple-300 font-medium\">Lenguajes:</span>\r\n                        <p className=\"text-gray-300 text-sm\">Lua, JavaScript, SQL, HTML/CSS</p>\r\n                      </div>\r\n                      <div>\r\n                        <span className=\"text-purple-300 font-medium\">Frameworks:</span>\r\n                        <p className=\"text-gray-300 text-sm\">React, Vue.js, QB-Core</p>\r\n                      </div>\r\n                      <div>\r\n                        <span className=\"text-purple-300 font-medium\">Herramientas:</span>\r\n                        <p className=\"text-gray-300 text-sm\">GitHub, FXServer, VPS</p>\r\n                      </div>\r\n                      <div>\r\n                        <span className=\"text-purple-300 font-medium\">Infraestructura:</span>\r\n                        <p className=\"text-gray-300 text-sm\">VPS, MySQL</p>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </motion.div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Experience Section */}\r\n      <section id=\"experiencia\" className=\"py-20 px-4 sm:px-6 lg:px-8\">\r\n        <div className=\"max-w-7xl mx-auto\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 60 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6 }}\r\n            viewport={{ once: true }}\r\n            className=\"text-center mb-16\"\r\n          >\r\n            <h2 className=\"text-4xl md:text-5xl font-bold text-white mb-6\">Experiencia Laboral</h2>\r\n            <div className=\"w-24 h-1 bg-gradient-to-r from-purple-400 to-pink-400 mx-auto\"></div>\r\n          </motion.div>\r\n\r\n          <div className=\"space-y-8\">\r\n            {/* AICOR Experience */}\r\n            <motion.div\r\n              initial={{ opacity: 0, x: -60 }}\r\n              whileInView={{ opacity: 1, x: 0 }}\r\n              transition={{ duration: 0.6 }}\r\n              viewport={{ once: true }}\r\n              className=\"bg-white/5 backdrop-blur-sm rounded-lg p-8 border border-white/10 hover:border-purple-400/50 transition-colors\"\r\n            >\r\n              <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6\">\r\n                <div>\r\n                  <h3 className=\"text-2xl font-bold text-white mb-2\">Desarrollador Multiplataforma (FCT)</h3>\r\n                  <h4 className=\"text-xl text-purple-300 mb-2\">AICOR Consultores Informáticos</h4>\r\n                  <span className=\"text-gray-400\">2024</span>\r\n                </div>\r\n                <div className=\"mt-4 lg:mt-0\">\r\n                  <span className=\"bg-green-500/20 text-green-300 px-3 py-1 rounded-full text-sm\">\r\n                    Formación en Centros de Trabajo\r\n                  </span>\r\n                </div>\r\n              </div>\r\n\r\n              <p className=\"text-gray-300 mb-4 leading-relaxed text-justify\">\r\n                Desarrollo de aplicaciones multiplataforma utilizando tecnologías modernas.\r\n                Participé en proyectos reales trabajando con frameworks y herramientas de desarrollo actuales.\r\n              </p>\r\n\r\n              <div className=\"grid md:grid-cols-2 gap-6\">\r\n                <div>\r\n                  <h5 className=\"text-lg font-semibold text-white mb-3\">Responsabilidades:</h5>\r\n                  <ul className=\"space-y-2 text-gray-300\">\r\n                    <li className=\"flex items-center gap-2\">\r\n                      <div className=\"w-2 h-2 bg-purple-400 rounded-full\"></div>\r\n                      Desarrollo con React Native\r\n                    </li>\r\n                    <li className=\"flex items-center gap-2\">\r\n                      <div className=\"w-2 h-2 bg-purple-400 rounded-full\"></div>\r\n                      Desarrollo frontend con React\r\n                    </li>\r\n                    <li className=\"flex items-center gap-2\">\r\n                      <div className=\"w-2 h-2 bg-purple-400 rounded-full\"></div>\r\n                      Desarrollo y mantenimiento en WordPress\r\n                    </li>\r\n                  </ul>\r\n                </div>\r\n                <div>\r\n                  <h5 className=\"text-lg font-semibold text-white mb-3\">Tecnologías:</h5>\r\n                  <div className=\"flex flex-wrap gap-2\">\r\n                    {['React', 'React Native', 'WordPress', 'PHP', 'JavaScript'].map((tech) => (\r\n                      <span key={tech} className=\"bg-purple-500/20 text-purple-300 px-3 py-1 rounded-full text-sm\">\r\n                        {tech}\r\n                      </span>\r\n                    ))}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </motion.div>\r\n\r\n            {/* Ayuntamiento Experience */}\r\n            <motion.div\r\n              initial={{ opacity: 0, x: 60 }}\r\n              whileInView={{ opacity: 1, x: 0 }}\r\n              transition={{ duration: 0.6 }}\r\n              viewport={{ once: true }}\r\n              className=\"bg-white/5 backdrop-blur-sm rounded-lg p-8 border border-white/10 hover:border-purple-400/50 transition-colors\"\r\n            >\r\n              <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6\">\r\n                <div>\r\n                  <h3 className=\"text-2xl font-bold text-white mb-2\">Técnico Auxiliar Informático (FCT)</h3>\r\n                  <h4 className=\"text-xl text-purple-300 mb-2\">Ayuntamiento de Córdoba</h4>\r\n                  <span className=\"text-gray-400\">2022</span>\r\n                </div>\r\n                <div className=\"mt-4 lg:mt-0\">\r\n                  <span className=\"bg-green-500/20 text-green-300 px-3 py-1 rounded-full text-sm\">\r\n                    Formación en Centros de Trabajo\r\n                  </span>\r\n                </div>\r\n              </div>\r\n\r\n              <p className=\"text-gray-300 mb-4 leading-relaxed text-justify\">\r\n                Experiencia en el mantenimiento y soporte técnico de equipos informáticos en el sector público.\r\n                Trabajo con hardware y sistemas de la administración local.\r\n              </p>\r\n\r\n              <div>\r\n                <h5 className=\"text-lg font-semibold text-white mb-3\">Responsabilidades:</h5>\r\n                <ul className=\"space-y-2 text-gray-300\">\r\n                  <li className=\"flex items-center gap-2\">\r\n                    <div className=\"w-2 h-2 bg-purple-400 rounded-full\"></div>\r\n                    Reparación de equipos informáticos\r\n                  </li>\r\n                  <li className=\"flex items-center gap-2\">\r\n                    <div className=\"w-2 h-2 bg-purple-400 rounded-full\"></div>\r\n                    Mantenimiento preventivo y correctivo\r\n                  </li>\r\n                  <li className=\"flex items-center gap-2\">\r\n                    <div className=\"w-2 h-2 bg-purple-400 rounded-full\"></div>\r\n                    Clonación y configuración de equipos\r\n                  </li>\r\n                  <li className=\"flex items-center gap-2\">\r\n                    <div className=\"w-2 h-2 bg-purple-400 rounded-full\"></div>\r\n                    Soporte técnico a usuarios\r\n                  </li>\r\n                </ul>\r\n              </div>\r\n            </motion.div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Contact Section */}\r\n      <section id=\"contacto\" className=\"py-20 px-4 sm:px-6 lg:px-8 bg-black/20\">\r\n        <div className=\"max-w-7xl mx-auto\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 60 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6 }}\r\n            viewport={{ once: true }}\r\n            className=\"text-center mb-16\"\r\n          >\r\n            <h2 className=\"text-4xl md:text-5xl font-bold text-white mb-6\">Contacto</h2>\r\n            <div className=\"w-24 h-1 bg-gradient-to-r from-purple-400 to-pink-400 mx-auto mb-6\"></div>\r\n            <p className=\"text-xl text-gray-300 max-w-3xl mx-auto text-center\">\r\n              ¿Tienes un proyecto en mente? ¡Me encantaría escuchar sobre él y ver cómo puedo ayudarte!\r\n            </p>\r\n          </motion.div>\r\n\r\n          <div className=\"flex justify-center\">\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 60 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6 }}\r\n              viewport={{ once: true }}\r\n              className=\"max-w-2xl w-full\"\r\n            >\r\n              <div className=\"grid md:grid-cols-2 gap-8\">\r\n                <div className=\"flex items-center gap-4 p-6 bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 hover:border-purple-400/50 transition-colors\">\r\n                  <div className=\"w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center\">\r\n                    <Mail className=\"w-6 h-6 text-purple-400\" />\r\n                  </div>\r\n                  <div>\r\n                    <h3 className=\"text-lg font-semibold text-white\">Email</h3>\r\n                    <a href=\"mailto:<EMAIL>\" className=\"text-purple-300 hover:text-purple-200 transition-colors\">\r\n                      <EMAIL>\r\n                    </a>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"flex items-center gap-4 p-6 bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 hover:border-purple-400/50 transition-colors\">\r\n                  <div className=\"w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center\">\r\n                    <Phone className=\"w-6 h-6 text-purple-400\" />\r\n                  </div>\r\n                  <div>\r\n                    <h3 className=\"text-lg font-semibold text-white\">Teléfono</h3>\r\n                    <a href=\"tel:+34634668535\" className=\"text-purple-300 hover:text-purple-200 transition-colors\">\r\n                      +34 634 668 535\r\n                    </a>\r\n                  </div>\r\n                </div>\r\n\r\n\r\n\r\n              </div>\r\n\r\n              <div className=\"flex justify-center gap-4 mt-8\">\r\n                <a\r\n                  href=\"https://github.com/gabrielmordev\"\r\n                  target=\"_blank\"\r\n                  rel=\"noopener noreferrer\"\r\n                  className=\"w-12 h-12 bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 hover:border-purple-400/50 flex items-center justify-center transition-colors group\"\r\n                >\r\n                  <Github className=\"w-6 h-6 text-gray-400 group-hover:text-purple-400 transition-colors\" />\r\n                </a>\r\n                <a\r\n                  href=\"https://www.linkedin.com/in/gabriel-moreno-munoz/\"\r\n                  target=\"_blank\"\r\n                  rel=\"noopener noreferrer\"\r\n                  className=\"w-12 h-12 bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 hover:border-purple-400/50 flex items-center justify-center transition-colors group\"\r\n                >\r\n                  <Linkedin className=\"w-6 h-6 text-gray-400 group-hover:text-purple-400 transition-colors\" />\r\n                </a>\r\n              </div>\r\n            </motion.div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Footer */}\r\n      <footer className=\"py-8 px-4 sm:px-6 lg:px-8 border-t border-white/10\">\r\n        <div className=\"max-w-7xl mx-auto text-center\">\r\n          <p className=\"text-gray-400\">\r\n            © 2025 Gabriel Moreno. Desarrollado con Next.js y Tailwind CSS.\r\n          </p>\r\n        </div>\r\n      </footer>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAMA,uBAAuB;AACvB,MAAM,WAAW,CAAC,EAAE,IAAI,EAAE,IAAI,EAAkC,iBAC9D,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,WAAU;oBACV,OAAO;wBAAE,iBAAiB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;oBAAC;;;;;;;;;;;0BAG7C,8OAAC;gBAAK,WAAU;0BAAyB;;;;;;;;;;;;AAI9B,SAAS;IACtB,MAAM,WAAW;QACf,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;IAC9B;IAEA,MAAM,mBAAmB;QACvB,SAAS;YACP,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,yBAAyB;IACzB,MAAM,gBAAgB;QACpB;YAAE,MAAM;YAAS,MAAM;QAA8E;QACrG;YAAE,MAAM;YAAU,MAAM;QAA8E;QACtG;YAAE,MAAM;YAAc,MAAM;QAAwF;QACpH;YAAE,MAAM;YAAc,MAAM;QAAwF;QACpH;YAAE,MAAM;YAAS,MAAM;QAA8E;QACrG;YAAE,MAAM;YAAQ,MAAM;QAA4E;KACnG;IAED,MAAM,eAAe;QACnB;YAAE,MAAM;YAAQ,MAAM;QAA4E;QAClG;YAAE,MAAM;YAAU,MAAM;QAAgF;QACxG;YAAE,MAAM;YAAW,MAAM;QAAgF;QACzG;YAAE,MAAM;YAAO,MAAM;QAA0E;QAC/F;YAAE,MAAM;YAAO,MAAM;QAA0E;KAChG;IAED,MAAM,gBAAgB;QACpB;YAAE,MAAM;YAAS,MAAM;QAA8E;QACrG;YAAE,MAAM;YAAW,MAAM;QAAkF;KAC5G;IAED,MAAM,aAAa;QACjB;YAAE,MAAM;YAAO,MAAM;QAA0E;QAC/F;YAAE,MAAM;YAAU,MAAM;QAAgF;QACxG;YAAE,MAAM;YAAa,MAAM;QAAsF;QACjH;YAAE,MAAM;YAAgB,MAAM;QAA8E;KAC7G;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC9B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,WAAU;0CACX;;;;;;0CAGD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,WAAU;0CAET;oCAAC;oCAAU;oCAAY;oCAAe;oCAAa;oCAAe;iCAAW,CAAC,GAAG,CAAC,CAAC,qBAClF,8OAAC;wCAEC,MAAM,CAAC,CAAC,EAAE,KAAK,WAAW,GAAG,OAAO,CAAC,KAAK,MAAM;wCAChD,WAAU;kDAET;uCAJI;;;;;;;;;;;;;;;;;;;;;;;;;;0BAajB,8OAAC;gBAAQ,IAAG;gBAAS,WAAU;0BAC7B,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,UAAU;wBACV,SAAQ;wBACR,SAAQ;wBACR,WAAU;;0CAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,UAAU;gCACV,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,OAAO;gDACP,QAAQ;gDACR,WAAU;gDACV,SAAS,CAAC;oDACR,8CAA8C;oDAC9C,EAAE,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG;oDAChC,MAAM,cAAc,EAAE,aAAa,CAAC,kBAAkB;oDACtD,IAAI,aAAa;wDACf,YAAY,KAAK,CAAC,OAAO,GAAG;oDAC9B;gDACF;;;;;;0DAEF,8OAAC;gDAAK,WAAU;0DAAuC;;;;;;;;;;;;;;;;;;;;;;0CAK7D,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;gCACR,UAAU;gCACV,WAAU;0CACX;;;;;;0CAID,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gCACP,UAAU;gCACV,WAAU;0CACX;;;;;;0CAID,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gCACP,UAAU;gCACV,WAAU;0CACX;;;;;;0CAKD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,UAAU;gCACV,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;0CAIV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,UAAU;gCACV,WAAU;0CAEV,cAAA,8OAAC;oCACC,MAAK;oCACL,UAAS;oCACT,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;wCACjE;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAShB,8OAAC;gBAAQ,IAAG;gBAAW,WAAU;0BAC/B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,8OAAC;oCAAI,WAAU;;;;;;;;;;;;sCAGjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,UAAU;wCAAE,MAAM;oCAAK;;sDAEvB,8OAAC;4CAAG,WAAU;sDAAqC;;;;;;sDACnD,8OAAC;4CAAE,WAAU;sDAAkD;;;;;;sDAG/D,8OAAC;4CAAE,WAAU;;gDAAkD;8DACb,8OAAC;oDAAO,WAAU;8DAAkB;;;;;;gDAAkB;8DAAE,8OAAC;oDAAO,WAAU;8DAAkB;;;;;;gDAAqB;;;;;;;sDAInK,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;8DAElC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;8DAElC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;8DAElC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;;;;;;;8CAMtC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAwC;;;;;;8DACtD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EAA8B;;;;;;8EAC5C,8OAAC;oEAAE,WAAU;8EAAgB;;;;;;;;;;;;sEAE/B,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EAA8B;;;;;;8EAC5C,8OAAC;oEAAE,WAAU;8EAAgB;;;;;;;;;;;;;;;;;;;;;;;;sDAKnC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAwC;;;;;;8DACtD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,8OAAC;oEAAK,WAAU;8EAAkB;;;;;;;;;;;;sEAEpC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,8OAAC;oEAAK,WAAU;8EAAkB;;;;;;;;;;;;;;;;;;;;;;;;sDAKxC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAwC;;;;;;8DACtD,8OAAC;oDAAE,WAAU;8DAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAW5D,8OAAC;gBAAQ,IAAG;gBAAc,WAAU;0BAClC,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,8OAAC;oCAAI,WAAU;;;;;;;;;;;;sCAGjB,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;;;;;;;sDAEnD,8OAAC;4CAAI,WAAU;sDACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,8OAAC;oDAAyB,MAAM,KAAK,IAAI;oDAAE,MAAM,KAAK,IAAI;mDAA3C,KAAK,IAAI;;;;;;;;;;;;;;;;8CAM9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;;;;;;;sDAEnD,8OAAC;4CAAI,WAAU;sDACZ,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC;oDAAyB,MAAM,KAAK,IAAI;oDAAE,MAAM,KAAK,IAAI;mDAA3C,KAAK,IAAI;;;;;;;;;;;;;;;;8CAM9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;;;;;;;sDAEnD,8OAAC;4CAAI,WAAU;sDAEZ,cAAc,GAAG,CAAC,CAAC,qBAClB,8OAAC;oDAAyB,MAAM,KAAK,IAAI;oDAAE,MAAM,KAAK,IAAI;mDAA3C,KAAK,IAAI;;;;;;;;;;;;;;;;8CAM9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,8MAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;;;;;;;sDAEnD,8OAAC;4CAAI,WAAU;sDACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;oDAAyB,MAAM,KAAK,IAAI;oDAAE,MAAM,KAAK,IAAI;mDAA3C,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUpC,8OAAC;gBAAQ,IAAG;gBAAY,WAAU;0BAChC,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,8OAAC;oCAAI,WAAU;;;;;;;;;;;;sCAGjB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,sNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;sEACxB,8OAAC;4DAAG,WAAU;sEAAgC;;;;;;sEAC9C,8OAAC;4DAAK,WAAU;sEAAkE;;;;;;;;;;;;8DAGpF,8OAAC;oDAAE,WAAU;8DAAkD;;;;;;8DAG/D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAwC;;;;;;sEACtD,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEAAG,WAAU;;sFACZ,8OAAC;4EAAI,WAAU;;;;;;wEAA2C;;;;;;;8EAG5D,8OAAC;oEAAG,WAAU;;sFACZ,8OAAC;4EAAI,WAAU;;;;;;wEAA2C;;;;;;;8EAG5D,8OAAC;oEAAG,WAAU;;sFACZ,8OAAC;4EAAI,WAAU;;;;;;wEAA2C;;;;;;;8EAG5D,8OAAC;oEAAG,WAAU;;sFACZ,8OAAC;4EAAI,WAAU;;;;;;wEAA2C;;;;;;;8EAG5D,8OAAC;oEAAG,WAAU;;sFACZ,8OAAC;4EAAI,WAAU;;;;;;wEAA2C;;;;;;;;;;;;;;;;;;;;;;;;;sDAOlE,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAwC;;;;;;kEACtD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFACC,8OAAC;wEAAK,WAAU;kFAA8B;;;;;;kFAC9C,8OAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;0EAEvC,8OAAC;;kFACC,8OAAC;wEAAK,WAAU;kFAA8B;;;;;;kFAC9C,8OAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;0EAEvC,8OAAC;;kFACC,8OAAC;wEAAK,WAAU;kFAA8B;;;;;;kFAC9C,8OAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;0EAEvC,8OAAC;;kFACC,8OAAC;wEAAK,WAAU;kFAA8B;;;;;;kFAC9C,8OAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAYvD,8OAAC;gBAAQ,IAAG;gBAAc,WAAU;0BAClC,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,8OAAC;oCAAI,WAAU;;;;;;;;;;;;sCAGjB,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAqC;;;;;;sEACnD,8OAAC;4DAAG,WAAU;sEAA+B;;;;;;sEAC7C,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;8DAElC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAAgE;;;;;;;;;;;;;;;;;sDAMpF,8OAAC;4CAAE,WAAU;sDAAkD;;;;;;sDAK/D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAwC;;;;;;sEACtD,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEAAG,WAAU;;sFACZ,8OAAC;4EAAI,WAAU;;;;;;wEAA2C;;;;;;;8EAG5D,8OAAC;oEAAG,WAAU;;sFACZ,8OAAC;4EAAI,WAAU;;;;;;wEAA2C;;;;;;;8EAG5D,8OAAC;oEAAG,WAAU;;sFACZ,8OAAC;4EAAI,WAAU;;;;;;wEAA2C;;;;;;;;;;;;;;;;;;;8DAKhE,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAwC;;;;;;sEACtD,8OAAC;4DAAI,WAAU;sEACZ;gEAAC;gEAAS;gEAAgB;gEAAa;gEAAO;6DAAa,CAAC,GAAG,CAAC,CAAC,qBAChE,8OAAC;oEAAgB,WAAU;8EACxB;mEADQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAUrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAqC;;;;;;sEACnD,8OAAC;4DAAG,WAAU;sEAA+B;;;;;;sEAC7C,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;8DAElC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAAgE;;;;;;;;;;;;;;;;;sDAMpF,8OAAC;4CAAE,WAAU;sDAAkD;;;;;;sDAK/D,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAwC;;;;;;8DACtD,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEAAI,WAAU;;;;;;gEAA2C;;;;;;;sEAG5D,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEAAI,WAAU;;;;;;gEAA2C;;;;;;;sEAG5D,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEAAI,WAAU;;;;;;gEAA2C;;;;;;;sEAG5D,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEAAI,WAAU;;;;;;gEAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWxE,8OAAC;gBAAQ,IAAG;gBAAW,WAAU;0BAC/B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAE,WAAU;8CAAsD;;;;;;;;;;;;sCAKrE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;kEAElB,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAmC;;;;;;0EACjD,8OAAC;gEAAE,MAAK;gEAAiC,WAAU;0EAA0D;;;;;;;;;;;;;;;;;;0DAMjH,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;;;;;;kEAEnB,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAmC;;;;;;0EACjD,8OAAC;gEAAE,MAAK;gEAAmB,WAAU;0EAA0D;;;;;;;;;;;;;;;;;;;;;;;;kDAUrG,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,MAAK;gDACL,QAAO;gDACP,KAAI;gDACJ,WAAU;0DAEV,cAAA,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;0DAEpB,8OAAC;gDACC,MAAK;gDACL,QAAO;gDACP,KAAI;gDACJ,WAAU;0DAEV,cAAA,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAShC,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;;;;;;AAOvC", "debugId": null}}]}