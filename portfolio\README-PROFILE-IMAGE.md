# Cómo añadir tu imagen de perfil

## 📸 Instrucciones para añadir tu foto

Para que tu imagen de perfil aparezca en el portfolio, sigue estos pasos:

### 1. Preparar la imagen
- **Formato recomendado**: JPG, PNG o WebP
- **<PERSON>a<PERSON> recomendado**: 400x400 píxeles o superior (cuadrada)
- **Peso**: Menos de 1MB para mejor rendimiento

### 2. Añadir la imagen al proyecto
1. Coloca tu imagen en la carpeta `public/` del proyecto
2. Renómbrala como `profile.jpg` (o cambia la extensión según corresponda)

### 3. Si usas un nombre diferente
Si quieres usar un nombre diferente para tu imagen, edita el archivo `src/app/page.tsx` en la línea que contiene:
```jsx
src="/profile.jpg"
```

Y cámbialo por el nombre de tu archivo:
```jsx
src="/tu-imagen.jpg"
```

### 4. Fallback automático
Si no añades ninguna imagen o la imagen no se puede cargar, el sistema automáticamente mostrará las iniciales "GM" como respaldo.

### 5. Optimización automática
Next.js optimizará automáticamente tu imagen para diferentes tamaños de pantalla y formatos, mejorando el rendimiento de carga.

## 🎨 Consejos para la mejor imagen de perfil

- **Fondo neutro**: Usa un fondo simple o transparente
- **Buena iluminación**: Asegúrate de que tu rostro esté bien iluminado
- **Centrado**: Tu rostro debe estar centrado en la imagen
- **Profesional**: Usa una imagen que refleje tu profesionalismo

## 🔧 Ejemplo de estructura de archivos

```
portfolio/
├── public/
│   ├── profile.jpg          ← Tu imagen aquí
│   ├── next.svg
│   └── ...
├── src/
│   └── app/
│       └── page.tsx
└── ...
```

¡Una vez que añadas tu imagen, se mostrará automáticamente en el portfolio!
