package com.example.petdates.viewmodel

import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import com.example.petdates.data.SampleData
import com.example.petdates.model.Match
import com.example.petdates.model.Pet
import java.util.Date

class PetDatesViewModel : ViewModel() {
    
    private val allPets = SampleData.samplePets.toMutableList()
    
    var currentPets by mutableStateOf(allPets.toList())
        private set
    
    var matches by mutableStateOf<List<Match>>(emptyList())
        private set
    
    var currentPetIndex by mutableStateOf(0)
        private set
    
    val currentPet: Pet?
        get() = if (currentPetIndex < currentPets.size) currentPets[currentPetIndex] else null
    
    val hasMorePets: Boolean
        get() = currentPetIndex < currentPets.size
    
    fun likePet() {
        currentPet?.let { pet ->
            // Simular que hay un 70% de probabilidad de match
            if (Math.random() < 0.7) {
                val match = Match(
                    id = "match_${pet.id}_${System.currentTimeMillis()}",
                    pet = pet,
                    matchDate = Date()
                )
                matches = matches + match
            }
            moveToNextPet()
        }
    }
    
    fun dislikePet() {
        moveToNextPet()
    }
    
    private fun moveToNextPet() {
        if (currentPetIndex < currentPets.size - 1) {
            currentPetIndex++
        } else {
            // Si no hay más mascotas, reiniciar con nuevas mascotas
            resetPets()
        }
    }
    
    private fun resetPets() {
        currentPetIndex = 0
        // En una app real, aquí cargarías más mascotas del servidor
        currentPets = allPets.shuffled()
    }
    
    fun markMatchAsViewed(matchId: String) {
        matches = matches.map { match ->
            if (match.id == matchId) {
                match.copy(isNewMatch = false)
            } else {
                match
            }
        }
    }
    
    fun getNewMatchesCount(): Int {
        return matches.count { it.isNewMatch }
    }
}
