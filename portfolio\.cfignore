# Cloudflare Pages ignore file
# Excluye archivos innecesarios para el despliegue

# Dependencies
node_modules/
.pnp/
.pnp.*

# Build cache y archivos temporales
.next/cache/
.next/standalone/
.next/static/chunks/webpack-*
cache/

# Archivos de desarrollo
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Archivos del sistema
.DS_Store
Thumbs.db

# Archivos de editor
.vscode/
.idea/
*.swp
*.swo

# Archivos de testing
coverage/
.nyc_output/

# Archivos TypeScript
*.tsbuildinfo

# Archivos de documentación de desarrollo
README-*.md
