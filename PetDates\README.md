# PetDates 🐕💕

Una aplicación Android tipo Tinder para mascotas, desarrollada con Jetpack Compose.

## Características

- **Swipe de mascotas**: Desliza hacia la derecha para dar "like" o hacia la izquierda para "dislike"
- **Sistema de matches**: <PERSON><PERSON>do dos mascotas se gustan mutuamente, se crea un match
- **Perfiles detallados**: Ve información completa de cada mascota
- **Interfaz moderna**: Diseñada con Material Design 3 y Jetpack Compose

## Funcionalidades

### 🏠 Pantalla Principal (Swipe)
- Tarjetas deslizables con fotos de mascotas
- Información básica: nombre, edad, raza, ubicación
- Botones de acción para like/dislike
- Indicador de matches nuevos

### 💕 Pantalla de Matches
- Lista de todas las mascotas que han hecho match
- Indicador de matches nuevos
- Fecha del match
- Acceso rápido al perfil completo

### 📋 Perfil de Mascota
- Foto en alta resolución
- Información completa: descripción, características, dueño
- Diseño atractivo con gradientes y cards

## Tecnologías Utilizadas

- **Kotlin**: Lenguaje de programación
- **Jetpack Compose**: UI moderna y declarativa
- **Navigation Compose**: Navegación entre pantallas
- **ViewModel**: Gestión de estado
- **Coil**: Carga de imágenes desde URLs
- **Material Design 3**: Sistema de diseño

## Estructura del Proyecto

```
app/src/main/java/com/example/petdates/
├── model/
│   ├── Pet.kt              # Modelo de datos de mascota
│   └── Match.kt            # Modelo de datos de match
├── data/
│   └── SampleData.kt       # Datos de ejemplo
├── viewmodel/
│   └── PetDatesViewModel.kt # Lógica de negocio
├── ui/
│   ├── components/
│   │   ├── PetCard.kt      # Tarjeta de mascota
│   │   └── SwipeableCard.kt # Tarjeta con gestos de swipe
│   └── screens/
│       ├── SwipeScreen.kt   # Pantalla principal
│       ├── MatchesScreen.kt # Pantalla de matches
│       └── PetDetailScreen.kt # Detalle de mascota
├── navigation/
│   └── PetDatesNavigation.kt # Sistema de navegación
└── MainActivity.kt
```

## Instalación

1. Clona el repositorio
2. Abre el proyecto en Android Studio
3. Sincroniza las dependencias de Gradle
4. Ejecuta la aplicación en un dispositivo o emulador

## Próximas Mejoras

- [ ] Integración con base de datos real
- [ ] Sistema de autenticación de usuarios
- [ ] Chat entre matches
- [ ] Filtros de búsqueda (edad, raza, ubicación)
- [ ] Subida de fotos desde la galería
- [ ] Notificaciones push para nuevos matches
- [ ] Geolocalización para mascotas cercanas

## Contribuir

¡Las contribuciones son bienvenidas! Por favor, abre un issue o envía un pull request.

## Licencia

Este proyecto está bajo la Licencia MIT.
