(()=>{var e={};e.id=974,e.ids=[974],e.modules={440:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>n});var r=i(1658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},512:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{default:function(){return f},defaultHead:function(){return d}});let r=i(4985),n=i(740),s=i(687),a=n._(i(3210)),o=r._(i(7755)),l=i(4959),u=i(9513),c=i(4604);function d(e){void 0===e&&(e=!1);let t=[(0,s.jsx)("meta",{charSet:"utf-8"},"charset")];return e||t.push((0,s.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),t}function h(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===a.default.Fragment?e.concat(a.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}i(148);let p=["name","httpEquiv","charSet","itemProp"];function m(e,t){let{inAmpMode:i}=t;return e.reduce(h,[]).reverse().concat(d(i).reverse()).filter(function(){let e=new Set,t=new Set,i=new Set,r={};return n=>{let s=!0,a=!1;if(n.key&&"number"!=typeof n.key&&n.key.indexOf("$")>0){a=!0;let t=n.key.slice(n.key.indexOf("$")+1);e.has(t)?s=!1:e.add(t)}switch(n.type){case"title":case"base":t.has(n.type)?s=!1:t.add(n.type);break;case"meta":for(let e=0,t=p.length;e<t;e++){let t=p[e];if(n.props.hasOwnProperty(t))if("charSet"===t)i.has(t)?s=!1:i.add(t);else{let e=n.props[t],i=r[t]||new Set;("name"!==t||!a)&&i.has(e)?s=!1:(i.add(e),r[t]=i)}}}return s}}()).reverse().map((e,t)=>{let r=e.key||t;if(process.env.__NEXT_OPTIMIZE_FONTS&&!i&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let t={...e.props||{}};return t["data-href"]=t.href,t.href=void 0,t["data-optimized-fonts"]=!0,a.default.cloneElement(e,t)}return a.default.cloneElement(e,{key:r})})}let f=function(e){let{children:t}=e,i=(0,a.useContext)(l.AmpStateContext),r=(0,a.useContext)(u.HeadManagerContext);return(0,s.jsx)(o.default,{reduceComponentsToState:m,headManager:r,inAmpMode:(0,c.isInAmpMode)(i),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},554:(e,t)=>{"use strict";function i(e){return e.endsWith("/route")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isAppRouteRoute",{enumerable:!0,get:function(){return i}})},660:(e,t)=>{"use strict";function i(e){let t=5381;for(let i=0;i<e.length;i++)t=(t<<5)+t+e.charCodeAt(i)|0;return t>>>0}function r(e){return i(e).toString(36).slice(0,5)}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{djb2Hash:function(){return i},hexHash:function(){return r}})},689:(e,t,i)=>{"use strict";i.r(t),i.d(t,{GlobalError:()=>a.a,__next_app__:()=>d,pages:()=>c,routeModule:()=>h,tree:()=>u});var r=i(5239),n=i(8088),s=i(8170),a=i.n(s),o=i(893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);i.d(t,l);let u={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(i.bind(i,1204)),"C:\\Users\\<USER>\\Desktop\\gabrielmordev\\portfolio\\src\\app\\page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(i.bind(i,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(i.bind(i,4431)),"C:\\Users\\<USER>\\Desktop\\gabrielmordev\\portfolio\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(i.t.bind(i,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(i.t.bind(i,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(i.t.bind(i,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(i.bind(i,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\gabrielmordev\\portfolio\\src\\app\\page.tsx"],d={require:i,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},1204:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>r});let r=(0,i(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\gabrielmordev\\\\portfolio\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\gabrielmordev\\portfolio\\src\\app\\page.tsx","default")},1261:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{default:function(){return l},getImageProps:function(){return o}});let r=i(4985),n=i(4953),s=i(6533),a=r._(i(1933));function o(e){let{props:t}=(0,n.getImgProps)(e,{defaultLoader:a.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,i]of Object.entries(t))void 0===i&&delete t[e];return{props:t}}let l=s.Image},1437:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{INTERCEPTION_ROUTE_MARKERS:function(){return n},extractInterceptionRouteInformation:function(){return a},isInterceptionRouteAppPath:function(){return s}});let r=i(4722),n=["(..)(..)","(.)","(..)","(...)"];function s(e){return void 0!==e.split("/").find(e=>n.find(t=>e.startsWith(t)))}function a(e){let t,i,s;for(let r of e.split("/"))if(i=n.find(e=>r.startsWith(e))){[t,s]=e.split(i,2);break}if(!t||!i||!s)throw Object.defineProperty(Error("Invalid interception route: "+e+". Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>"),"__NEXT_ERROR_CODE",{value:"E269",enumerable:!1,configurable:!0});switch(t=(0,r.normalizeAppPath)(t),i){case"(.)":s="/"===t?"/"+s:t+"/"+s;break;case"(..)":if("/"===t)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..) marker at the root level, use (.) instead."),"__NEXT_ERROR_CODE",{value:"E207",enumerable:!1,configurable:!0});s=t.split("/").slice(0,-1).concat(s).join("/");break;case"(...)":s="/"+s;break;case"(..)(..)":let a=t.split("/");if(a.length<=2)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..)(..) marker at the root level or one level up."),"__NEXT_ERROR_CODE",{value:"E486",enumerable:!1,configurable:!0});s=a.slice(0,-2).concat(s).join("/");break;default:throw Object.defineProperty(Error("Invariant: unexpected marker"),"__NEXT_ERROR_CODE",{value:"E112",enumerable:!1,configurable:!0})}return{interceptingRoute:t,interceptedRoute:s}}},1480:(e,t)=>{"use strict";function i(e){let{widthInt:t,heightInt:i,blurWidth:r,blurHeight:n,blurDataURL:s,objectFit:a}=e,o=r?40*r:t,l=n?40*n:i,u=o&&l?"viewBox='0 0 "+o+" "+l+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+u+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(u?"none":"contain"===a?"xMidYMid":"cover"===a?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+s+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return i}})},1612:(e,t,i)=>{Promise.resolve().then(i.bind(i,8806))},1658:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{fillMetadataSegment:function(){return h},normalizeMetadataPageToRoute:function(){return m},normalizeMetadataRoute:function(){return p}});let r=i(8304),n=function(e){return e&&e.__esModule?e:{default:e}}(i(8671)),s=i(6341),a=i(4396),o=i(660),l=i(4722),u=i(2958),c=i(5499);function d(e){let t=n.default.dirname(e);if(e.endsWith("/sitemap"))return"";let i="";return t.split("/").some(e=>(0,c.isGroupSegment)(e)||(0,c.isParallelRouteSegment)(e))&&(i=(0,o.djb2Hash)(t).toString(36).slice(0,6)),i}function h(e,t,i){let r=(0,l.normalizeAppPath)(e),o=(0,a.getNamedRouteRegex)(r,{prefixRouteKeys:!1}),c=(0,s.interpolateDynamicPath)(r,t,o),{name:h,ext:p}=n.default.parse(i),m=d(n.default.posix.join(e,h)),f=m?`-${m}`:"";return(0,u.normalizePathSep)(n.default.join(c,`${h}${f}${p}`))}function p(e){if(!(0,r.isMetadataPage)(e))return e;let t=e,i="";if("/robots"===e?t+=".txt":"/manifest"===e?t+=".webmanifest":i=d(e),!t.endsWith("/route")){let{dir:e,name:r,ext:s}=n.default.parse(t);t=n.default.posix.join(e,`${r}${i?`-${i}`:""}${s}`,"route")}return t}function m(e,t){let i=e.endsWith("/route"),r=i?e.slice(0,-6):e,n=r.endsWith("/sitemap")?".xml":"";return(t?`${r}/[__metadata_id__]`:`${r}${n}`)+(i?"/route":"")}},1933:(e,t)=>{"use strict";function i(e){var t;let{config:i,src:r,width:n,quality:s}=e,a=s||(null==(t=i.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return i.path+"?url="+encodeURIComponent(r)+"&w="+n+"&q="+a+(r.startsWith("/_next/static/media/"),"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return r}}),i.__next_img_default=!0;let r=i},2437:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getPathMatch",{enumerable:!0,get:function(){return n}});let r=i(5362);function n(e,t){let i=[],n=(0,r.pathToRegexp)(e,i,{delimiter:"/",sensitive:"boolean"==typeof(null==t?void 0:t.sensitive)&&t.sensitive,strict:null==t?void 0:t.strict}),s=(0,r.regexpToFunction)((null==t?void 0:t.regexModifier)?new RegExp(t.regexModifier(n.source),n.flags):n,i);return(e,r)=>{if("string"!=typeof e)return!1;let n=s(e);if(!n)return!1;if(null==t?void 0:t.removeUnnamedParams)for(let e of i)"number"==typeof e.name&&delete n.params[e.name];return{...r,...n.params}}}},2756:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{VALID_LOADERS:function(){return i},imageConfigDefault:function(){return r}});let i=["default","imgix","cloudinary","akamai","custom"],r={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},2785:(e,t)=>{"use strict";function i(e){let t={};for(let[i,r]of e.entries()){let e=t[i];void 0===e?t[i]=r:Array.isArray(e)?e.push(r):t[i]=[e,r]}return t}function r(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function n(e){let t=new URLSearchParams;for(let[i,n]of Object.entries(e))if(Array.isArray(n))for(let e of n)t.append(i,r(e));else t.set(i,r(n));return t}function s(e){for(var t=arguments.length,i=Array(t>1?t-1:0),r=1;r<t;r++)i[r-1]=arguments[r];for(let t of i){for(let i of t.keys())e.delete(i);for(let[i,r]of t.entries())e.append(i,r)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{assign:function(){return s},searchParamsToUrlQuery:function(){return i},urlQueryToSearchParams:function(){return n}})},2958:(e,t)=>{"use strict";function i(e){return e.replace(/\\/g,"/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathSep",{enumerable:!0,get:function(){return i}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3038:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return n}});let r=i(3210);function n(e,t){let i=(0,r.useRef)(null),n=(0,r.useRef)(null);return(0,r.useCallback)(r=>{if(null===r){let e=i.current;e&&(i.current=null,e());let t=n.current;t&&(n.current=null,t())}else e&&(i.current=s(e,r)),t&&(n.current=s(t,r))},[e,t])}function s(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let i=e(t);return"function"==typeof i?i:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3293:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"escapeStringRegexp",{enumerable:!0,get:function(){return n}});let i=/[|\\{}()[\]^$+*?.-]/,r=/[|\\{}()[\]^$+*?.-]/g;function n(e){return i.test(e)?e.replace(r,"\\$&"):e}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3736:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseRelativeUrl",{enumerable:!0,get:function(){return n}}),i(4827);let r=i(2785);function n(e,t,i){void 0===i&&(i=!0);let n=new URL("http://n"),s=t?new URL(t,n):e.startsWith(".")?new URL("http://n"):n,{pathname:a,searchParams:o,search:l,hash:u,href:c,origin:d}=new URL(e,s);if(d!==n.origin)throw Object.defineProperty(Error("invariant: invalid relative URL, router received "+e),"__NEXT_ERROR_CODE",{value:"E159",enumerable:!1,configurable:!0});return{pathname:a,query:i?(0,r.searchParamsToUrlQuery)(o):void 0,search:l,hash:u,href:c.slice(d.length)}}},3873:e=>{"use strict";e.exports=require("path")},4396:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{getNamedMiddlewareRegex:function(){return f},getNamedRouteRegex:function(){return m},getRouteRegex:function(){return d},parseParameter:function(){return l}});let r=i(6143),n=i(1437),s=i(3293),a=i(2887),o=/^([^[]*)\[((?:\[[^\]]*\])|[^\]]+)\](.*)$/;function l(e){let t=e.match(o);return t?u(t[2]):u(e)}function u(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let i=e.startsWith("...");return i&&(e=e.slice(3)),{key:e,repeat:i,optional:t}}function c(e,t,i){let r={},l=1,c=[];for(let d of(0,a.removeTrailingSlash)(e).slice(1).split("/")){let e=n.INTERCEPTION_ROUTE_MARKERS.find(e=>d.startsWith(e)),a=d.match(o);if(e&&a&&a[2]){let{key:t,optional:i,repeat:n}=u(a[2]);r[t]={pos:l++,repeat:n,optional:i},c.push("/"+(0,s.escapeStringRegexp)(e)+"([^/]+?)")}else if(a&&a[2]){let{key:e,repeat:t,optional:n}=u(a[2]);r[e]={pos:l++,repeat:t,optional:n},i&&a[1]&&c.push("/"+(0,s.escapeStringRegexp)(a[1]));let o=t?n?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)";i&&a[1]&&(o=o.substring(1)),c.push(o)}else c.push("/"+(0,s.escapeStringRegexp)(d));t&&a&&a[3]&&c.push((0,s.escapeStringRegexp)(a[3]))}return{parameterizedRoute:c.join(""),groups:r}}function d(e,t){let{includeSuffix:i=!1,includePrefix:r=!1,excludeOptionalTrailingSlash:n=!1}=void 0===t?{}:t,{parameterizedRoute:s,groups:a}=c(e,i,r),o=s;return n||(o+="(?:/)?"),{re:RegExp("^"+o+"$"),groups:a}}function h(e){let t,{interceptionMarker:i,getSafeRouteKey:r,segment:n,routeKeys:a,keyPrefix:o,backreferenceDuplicateKeys:l}=e,{key:c,optional:d,repeat:h}=u(n),p=c.replace(/\W/g,"");o&&(p=""+o+p);let m=!1;(0===p.length||p.length>30)&&(m=!0),isNaN(parseInt(p.slice(0,1)))||(m=!0),m&&(p=r());let f=p in a;o?a[p]=""+o+c:a[p]=c;let g=i?(0,s.escapeStringRegexp)(i):"";return t=f&&l?"\\k<"+p+">":h?"(?<"+p+">.+?)":"(?<"+p+">[^/]+?)",d?"(?:/"+g+t+")?":"/"+g+t}function p(e,t,i,l,u){let c,d=(c=0,()=>{let e="",t=++c;for(;t>0;)e+=String.fromCharCode(97+(t-1)%26),t=Math.floor((t-1)/26);return e}),p={},m=[];for(let c of(0,a.removeTrailingSlash)(e).slice(1).split("/")){let e=n.INTERCEPTION_ROUTE_MARKERS.some(e=>c.startsWith(e)),a=c.match(o);if(e&&a&&a[2])m.push(h({getSafeRouteKey:d,interceptionMarker:a[1],segment:a[2],routeKeys:p,keyPrefix:t?r.NEXT_INTERCEPTION_MARKER_PREFIX:void 0,backreferenceDuplicateKeys:u}));else if(a&&a[2]){l&&a[1]&&m.push("/"+(0,s.escapeStringRegexp)(a[1]));let e=h({getSafeRouteKey:d,segment:a[2],routeKeys:p,keyPrefix:t?r.NEXT_QUERY_PARAM_PREFIX:void 0,backreferenceDuplicateKeys:u});l&&a[1]&&(e=e.substring(1)),m.push(e)}else m.push("/"+(0,s.escapeStringRegexp)(c));i&&a&&a[3]&&m.push((0,s.escapeStringRegexp)(a[3]))}return{namedParameterizedRoute:m.join(""),routeKeys:p}}function m(e,t){var i,r,n;let s=p(e,t.prefixRouteKeys,null!=(i=t.includeSuffix)&&i,null!=(r=t.includePrefix)&&r,null!=(n=t.backreferenceDuplicateKeys)&&n),a=s.namedParameterizedRoute;return t.excludeOptionalTrailingSlash||(a+="(?:/)?"),{...d(e,t),namedRegex:"^"+a+"$",routeKeys:s.routeKeys}}function f(e,t){let{parameterizedRoute:i}=c(e,!1,!1),{catchAll:r=!0}=t;if("/"===i)return{namedRegex:"^/"+(r?".*":"")+"$"};let{namedParameterizedRoute:n}=p(e,!1,!1,!1,!1);return{namedRegex:"^"+n+(r?"(?:(/.*)?)":"")+"$"}}},4431:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>u,metadata:()=>l});var r=i(7413),n=i(2376),s=i.n(n),a=i(8726),o=i.n(a);i(1135);let l={title:"gabrielmordev",description:"gabrielmordev"};function u({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:`${s().variable} ${o().variable} antialiased`,children:e})})}},4604:(e,t)=>{"use strict";function i(e){let{ampFirst:t=!1,hybrid:i=!1,hasQuery:r=!1}=void 0===e?{}:e;return t||i&&r}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return i}})},4722:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{normalizeAppPath:function(){return s},normalizeRscURL:function(){return a}});let r=i(5531),n=i(5499);function s(e){return(0,r.ensureLeadingSlash)(e.split("/").reduce((e,t,i,r)=>!t||(0,n.isGroupSegment)(t)||"@"===t[0]||("page"===t||"route"===t)&&i===r.length-1?e:e+"/"+t,""))}function a(e){return e.replace(/\.rsc($|\?)/,"$1")}},4764:(e,t,i)=>{Promise.resolve().then(i.bind(i,1204))},4827:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{DecodeError:function(){return m},MiddlewareNotFoundError:function(){return y},MissingStaticPage:function(){return v},NormalizeError:function(){return f},PageNotFoundError:function(){return g},SP:function(){return h},ST:function(){return p},WEB_VITALS:function(){return i},execOnce:function(){return r},getDisplayName:function(){return l},getLocationOrigin:function(){return a},getURL:function(){return o},isAbsoluteUrl:function(){return s},isResSent:function(){return u},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return x}});let i=["CLS","FCP","FID","INP","LCP","TTFB"];function r(e){let t,i=!1;return function(){for(var r=arguments.length,n=Array(r),s=0;s<r;s++)n[s]=arguments[s];return i||(i=!0,t=e(...n)),t}}let n=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,s=e=>n.test(e);function a(){let{protocol:e,hostname:t,port:i}=window.location;return e+"//"+t+(i?":"+i:"")}function o(){let{href:e}=window.location,t=a();return e.substring(t.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function u(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let i=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let r=await e.getInitialProps(t);if(i&&u(i))return r;if(!r)throw Object.defineProperty(Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+r+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return r}let h="undefined"!=typeof performance,p=h&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class m extends Error{}class f extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class v extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class y extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function x(e){return JSON.stringify({message:e.message,stack:e.stack})}},4953:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return l}}),i(148);let r=i(1480),n=i(2756),s=["-moz-initial","fill","none","scale-down",void 0];function a(e){return void 0!==e.default}function o(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function l(e,t){var i,l;let u,c,d,{src:h,sizes:p,unoptimized:m=!1,priority:f=!1,loading:g,className:v,quality:y,width:x,height:b,fill:w=!1,style:j,overrideSrc:P,onLoad:E,onLoadingComplete:T,placeholder:S="empty",blurDataURL:A,fetchPriority:R,decoding:M="async",layout:N,objectFit:C,objectPosition:k,lazyBoundary:_,lazyRoot:D,...O}=e,{imgConf:V,showAltText:L,blurComplete:I,defaultLoader:F}=t,U=V||n.imageConfigDefault;if("allSizes"in U)u=U;else{let e=[...U.deviceSizes,...U.imageSizes].sort((e,t)=>e-t),t=U.deviceSizes.sort((e,t)=>e-t),r=null==(i=U.qualities)?void 0:i.sort((e,t)=>e-t);u={...U,allSizes:e,deviceSizes:t,qualities:r}}if(void 0===F)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let $=O.loader||F;delete O.loader,delete O.srcSet;let B="__next_img_default"in $;if(B){if("custom"===u.loader)throw Object.defineProperty(Error('Image with src "'+h+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=$;$=t=>{let{config:i,...r}=t;return e(r)}}if(N){"fill"===N&&(w=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[N];e&&(j={...j,...e});let t={responsive:"100vw",fill:"100vw"}[N];t&&!p&&(p=t)}let z="",W=o(x),q=o(b);if((l=h)&&"object"==typeof l&&(a(l)||void 0!==l.src)){let e=a(h)?h.default:h;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(c=e.blurWidth,d=e.blurHeight,A=A||e.blurDataURL,z=e.src,!w)if(W||q){if(W&&!q){let t=W/e.width;q=Math.round(e.height*t)}else if(!W&&q){let t=q/e.height;W=Math.round(e.width*t)}}else W=e.width,q=e.height}let H=!f&&("lazy"===g||void 0===g);(!(h="string"==typeof h?h:z)||h.startsWith("data:")||h.startsWith("blob:"))&&(m=!0,H=!1),u.unoptimized&&(m=!0),B&&!u.dangerouslyAllowSVG&&h.split("?",1)[0].endsWith(".svg")&&(m=!0);let X=o(y),G=Object.assign(w?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:C,objectPosition:k}:{},L?{}:{color:"transparent"},j),K=I||"empty"===S?null:"blur"===S?'url("data:image/svg+xml;charset=utf-8,'+(0,r.getImageBlurSvg)({widthInt:W,heightInt:q,blurWidth:c,blurHeight:d,blurDataURL:A||"",objectFit:G.objectFit})+'")':'url("'+S+'")',Y=s.includes(G.objectFit)?"fill"===G.objectFit?"100% 100%":"cover":G.objectFit,Z=K?{backgroundSize:Y,backgroundPosition:G.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:K}:{},Q=function(e){let{config:t,src:i,unoptimized:r,width:n,quality:s,sizes:a,loader:o}=e;if(r)return{src:i,srcSet:void 0,sizes:void 0};let{widths:l,kind:u}=function(e,t,i){let{deviceSizes:r,allSizes:n}=e;if(i){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let r;r=e.exec(i);)t.push(parseInt(r[2]));if(t.length){let e=.01*Math.min(...t);return{widths:n.filter(t=>t>=r[0]*e),kind:"w"}}return{widths:n,kind:"w"}}return"number"!=typeof t?{widths:r,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>n.find(t=>t>=e)||n[n.length-1]))],kind:"x"}}(t,n,a),c=l.length-1;return{sizes:a||"w"!==u?a:"100vw",srcSet:l.map((e,r)=>o({config:t,src:i,quality:s,width:e})+" "+("w"===u?e:r+1)+u).join(", "),src:o({config:t,src:i,quality:s,width:l[c]})}}({config:u,src:h,unoptimized:m,width:W,quality:X,sizes:p,loader:$});return{props:{...O,loading:H?"lazy":g,fetchPriority:R,width:W,height:q,decoding:M,className:v,style:{...G,...Z},sizes:Q.sizes,srcSet:Q.srcSet,src:P||Q.src},meta:{unoptimized:m,priority:f,placeholder:S,fill:w}}}},4959:(e,t,i)=>{"use strict";e.exports=i(4041).vendored.contexts.AmpContext},5362:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{function e(e,t){void 0===t&&(t={});for(var i=function(e){for(var t=[],i=0;i<e.length;){var r=e[i];if("*"===r||"+"===r||"?"===r){t.push({type:"MODIFIER",index:i,value:e[i++]});continue}if("\\"===r){t.push({type:"ESCAPED_CHAR",index:i++,value:e[i++]});continue}if("{"===r){t.push({type:"OPEN",index:i,value:e[i++]});continue}if("}"===r){t.push({type:"CLOSE",index:i,value:e[i++]});continue}if(":"===r){for(var n="",s=i+1;s<e.length;){var a=e.charCodeAt(s);if(a>=48&&a<=57||a>=65&&a<=90||a>=97&&a<=122||95===a){n+=e[s++];continue}break}if(!n)throw TypeError("Missing parameter name at "+i);t.push({type:"NAME",index:i,value:n}),i=s;continue}if("("===r){var o=1,l="",s=i+1;if("?"===e[s])throw TypeError('Pattern cannot start with "?" at '+s);for(;s<e.length;){if("\\"===e[s]){l+=e[s++]+e[s++];continue}if(")"===e[s]){if(0==--o){s++;break}}else if("("===e[s]&&(o++,"?"!==e[s+1]))throw TypeError("Capturing groups are not allowed at "+s);l+=e[s++]}if(o)throw TypeError("Unbalanced pattern at "+i);if(!l)throw TypeError("Missing pattern at "+i);t.push({type:"PATTERN",index:i,value:l}),i=s;continue}t.push({type:"CHAR",index:i,value:e[i++]})}return t.push({type:"END",index:i,value:""}),t}(e),r=t.prefixes,s=void 0===r?"./":r,a="[^"+n(t.delimiter||"/#?")+"]+?",o=[],l=0,u=0,c="",d=function(e){if(u<i.length&&i[u].type===e)return i[u++].value},h=function(e){var t=d(e);if(void 0!==t)return t;var r=i[u];throw TypeError("Unexpected "+r.type+" at "+r.index+", expected "+e)},p=function(){for(var e,t="";e=d("CHAR")||d("ESCAPED_CHAR");)t+=e;return t};u<i.length;){var m=d("CHAR"),f=d("NAME"),g=d("PATTERN");if(f||g){var v=m||"";-1===s.indexOf(v)&&(c+=v,v=""),c&&(o.push(c),c=""),o.push({name:f||l++,prefix:v,suffix:"",pattern:g||a,modifier:d("MODIFIER")||""});continue}var y=m||d("ESCAPED_CHAR");if(y){c+=y;continue}if(c&&(o.push(c),c=""),d("OPEN")){var v=p(),x=d("NAME")||"",b=d("PATTERN")||"",w=p();h("CLOSE"),o.push({name:x||(b?l++:""),pattern:x&&!b?a:b,prefix:v,suffix:w,modifier:d("MODIFIER")||""});continue}h("END")}return o}function i(e,t){void 0===t&&(t={});var i=s(t),r=t.encode,n=void 0===r?function(e){return e}:r,a=t.validate,o=void 0===a||a,l=e.map(function(e){if("object"==typeof e)return RegExp("^(?:"+e.pattern+")$",i)});return function(t){for(var i="",r=0;r<e.length;r++){var s=e[r];if("string"==typeof s){i+=s;continue}var a=t?t[s.name]:void 0,u="?"===s.modifier||"*"===s.modifier,c="*"===s.modifier||"+"===s.modifier;if(Array.isArray(a)){if(!c)throw TypeError('Expected "'+s.name+'" to not repeat, but got an array');if(0===a.length){if(u)continue;throw TypeError('Expected "'+s.name+'" to not be empty')}for(var d=0;d<a.length;d++){var h=n(a[d],s);if(o&&!l[r].test(h))throw TypeError('Expected all "'+s.name+'" to match "'+s.pattern+'", but got "'+h+'"');i+=s.prefix+h+s.suffix}continue}if("string"==typeof a||"number"==typeof a){var h=n(String(a),s);if(o&&!l[r].test(h))throw TypeError('Expected "'+s.name+'" to match "'+s.pattern+'", but got "'+h+'"');i+=s.prefix+h+s.suffix;continue}if(!u){var p=c?"an array":"a string";throw TypeError('Expected "'+s.name+'" to be '+p)}}return i}}function r(e,t,i){void 0===i&&(i={});var r=i.decode,n=void 0===r?function(e){return e}:r;return function(i){var r=e.exec(i);if(!r)return!1;for(var s=r[0],a=r.index,o=Object.create(null),l=1;l<r.length;l++)!function(e){if(void 0!==r[e]){var i=t[e-1];"*"===i.modifier||"+"===i.modifier?o[i.name]=r[e].split(i.prefix+i.suffix).map(function(e){return n(e,i)}):o[i.name]=n(r[e],i)}}(l);return{path:s,index:a,params:o}}}function n(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function s(e){return e&&e.sensitive?"":"i"}function a(e,t,i){void 0===i&&(i={});for(var r=i.strict,a=void 0!==r&&r,o=i.start,l=i.end,u=i.encode,c=void 0===u?function(e){return e}:u,d="["+n(i.endsWith||"")+"]|$",h="["+n(i.delimiter||"/#?")+"]",p=void 0===o||o?"^":"",m=0;m<e.length;m++){var f=e[m];if("string"==typeof f)p+=n(c(f));else{var g=n(c(f.prefix)),v=n(c(f.suffix));if(f.pattern)if(t&&t.push(f),g||v)if("+"===f.modifier||"*"===f.modifier){var y="*"===f.modifier?"?":"";p+="(?:"+g+"((?:"+f.pattern+")(?:"+v+g+"(?:"+f.pattern+"))*)"+v+")"+y}else p+="(?:"+g+"("+f.pattern+")"+v+")"+f.modifier;else p+="("+f.pattern+")"+f.modifier;else p+="(?:"+g+v+")"+f.modifier}}if(void 0===l||l)a||(p+=h+"?"),p+=i.endsWith?"(?="+d+")":"$";else{var x=e[e.length-1],b="string"==typeof x?h.indexOf(x[x.length-1])>-1:void 0===x;a||(p+="(?:"+h+"(?="+d+"))?"),b||(p+="(?="+h+"|"+d+")")}return new RegExp(p,s(i))}function o(t,i,r){if(t instanceof RegExp){if(!i)return t;var n=t.source.match(/\((?!\?)/g);if(n)for(var l=0;l<n.length;l++)i.push({name:l,prefix:"",suffix:"",modifier:"",pattern:""});return t}return Array.isArray(t)?RegExp("(?:"+t.map(function(e){return o(e,i,r).source}).join("|")+")",s(r)):a(e(t,r),i,r)}Object.defineProperty(t,"__esModule",{value:!0}),t.parse=e,t.compile=function(t,r){return i(e(t,r),r)},t.tokensToFunction=i,t.match=function(e,t){var i=[];return r(o(e,i,t),i,t)},t.regexpToFunction=r,t.tokensToRegexp=a,t.pathToRegexp=o})(),e.exports=t})()},5526:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{compileNonPath:function(){return c},matchHas:function(){return u},parseDestination:function(){return d},prepareDestination:function(){return h}});let r=i(5362),n=i(3293),s=i(6759),a=i(1437),o=i(8212);function l(e){return e.replace(/__ESC_COLON_/gi,":")}function u(e,t,i,r){void 0===i&&(i=[]),void 0===r&&(r=[]);let n={},s=i=>{let r,s=i.key;switch(i.type){case"header":s=s.toLowerCase(),r=e.headers[s];break;case"cookie":r="cookies"in e?e.cookies[i.key]:(0,o.getCookieParser)(e.headers)()[i.key];break;case"query":r=t[s];break;case"host":{let{host:t}=(null==e?void 0:e.headers)||{};r=null==t?void 0:t.split(":",1)[0].toLowerCase()}}if(!i.value&&r)return n[function(e){let t="";for(let i=0;i<e.length;i++){let r=e.charCodeAt(i);(r>64&&r<91||r>96&&r<123)&&(t+=e[i])}return t}(s)]=r,!0;if(r){let e=RegExp("^"+i.value+"$"),t=Array.isArray(r)?r.slice(-1)[0].match(e):r.match(e);if(t)return Array.isArray(t)&&(t.groups?Object.keys(t.groups).forEach(e=>{n[e]=t.groups[e]}):"host"===i.type&&t[0]&&(n.host=t[0])),!0}return!1};return!(!i.every(e=>s(e))||r.some(e=>s(e)))&&n}function c(e,t){if(!e.includes(":"))return e;for(let i of Object.keys(t))e.includes(":"+i)&&(e=e.replace(RegExp(":"+i+"\\*","g"),":"+i+"--ESCAPED_PARAM_ASTERISKS").replace(RegExp(":"+i+"\\?","g"),":"+i+"--ESCAPED_PARAM_QUESTION").replace(RegExp(":"+i+"\\+","g"),":"+i+"--ESCAPED_PARAM_PLUS").replace(RegExp(":"+i+"(?!\\w)","g"),"--ESCAPED_PARAM_COLON"+i));return e=e.replace(/(:|\*|\?|\+|\(|\)|\{|\})/g,"\\$1").replace(/--ESCAPED_PARAM_PLUS/g,"+").replace(/--ESCAPED_PARAM_COLON/g,":").replace(/--ESCAPED_PARAM_QUESTION/g,"?").replace(/--ESCAPED_PARAM_ASTERISKS/g,"*"),(0,r.compile)("/"+e,{validate:!1})(t).slice(1)}function d(e){let t=e.destination;for(let i of Object.keys({...e.params,...e.query}))i&&(t=t.replace(RegExp(":"+(0,n.escapeStringRegexp)(i),"g"),"__ESC_COLON_"+i));let i=(0,s.parseUrl)(t),r=i.pathname;r&&(r=l(r));let a=i.href;a&&(a=l(a));let o=i.hostname;o&&(o=l(o));let u=i.hash;return u&&(u=l(u)),{...i,pathname:r,hostname:o,href:a,hash:u}}function h(e){let t,i,n=Object.assign({},e.query),s=d(e),{hostname:o,query:u}=s,h=s.pathname;s.hash&&(h=""+h+s.hash);let p=[],m=[];for(let e of((0,r.pathToRegexp)(h,m),m))p.push(e.name);if(o){let e=[];for(let t of((0,r.pathToRegexp)(o,e),e))p.push(t.name)}let f=(0,r.compile)(h,{validate:!1});for(let[i,n]of(o&&(t=(0,r.compile)(o,{validate:!1})),Object.entries(u)))Array.isArray(n)?u[i]=n.map(t=>c(l(t),e.params)):"string"==typeof n&&(u[i]=c(l(n),e.params));let g=Object.keys(e.params).filter(e=>"nextInternalLocale"!==e);if(e.appendParamsToQuery&&!g.some(e=>p.includes(e)))for(let t of g)t in u||(u[t]=e.params[t]);if((0,a.isInterceptionRouteAppPath)(h))for(let t of h.split("/")){let i=a.INTERCEPTION_ROUTE_MARKERS.find(e=>t.startsWith(e));if(i){"(..)(..)"===i?(e.params["0"]="(..)",e.params["1"]="(..)"):e.params["0"]=i;break}}try{let[r,n]=(i=f(e.params)).split("#",2);t&&(s.hostname=t(e.params)),s.pathname=r,s.hash=(n?"#":"")+(n||""),delete s.search}catch(e){if(e.message.match(/Expected .*? to not repeat, but got an array/))throw Object.defineProperty(Error("To use a multi-match in the destination you must add `*` at the end of the param name to signify it should repeat. https://nextjs.org/docs/messages/invalid-multi-match"),"__NEXT_ERROR_CODE",{value:"E329",enumerable:!1,configurable:!0});throw e}return s.query={...n,...s.query},{newUrl:i,destQuery:u,parsedDestination:s}}},5531:(e,t)=>{"use strict";function i(e){return e.startsWith("/")?e:"/"+e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ensureLeadingSlash",{enumerable:!0,get:function(){return i}})},5863:(e,t,i)=>{Promise.resolve().then(i.t.bind(i,6346,23)),Promise.resolve().then(i.t.bind(i,7924,23)),Promise.resolve().then(i.t.bind(i,5656,23)),Promise.resolve().then(i.t.bind(i,99,23)),Promise.resolve().then(i.t.bind(i,8243,23)),Promise.resolve().then(i.t.bind(i,8827,23)),Promise.resolve().then(i.t.bind(i,2763,23)),Promise.resolve().then(i.t.bind(i,7173,23))},6341:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{getPreviouslyRevalidatedTags:function(){return v},getUtils:function(){return g},interpolateDynamicPath:function(){return m},normalizeDynamicRouteParams:function(){return f},normalizeVercelUrl:function(){return p}});let r=i(9551),n=i(1959),s=i(2437),a=i(4396),o=i(8034),l=i(5526),u=i(2887),c=i(4722),d=i(6143),h=i(7912);function p(e,t,i){let n=(0,r.parse)(e.url,!0);for(let e of(delete n.search,Object.keys(n.query))){let r=e!==d.NEXT_QUERY_PARAM_PREFIX&&e.startsWith(d.NEXT_QUERY_PARAM_PREFIX),s=e!==d.NEXT_INTERCEPTION_MARKER_PREFIX&&e.startsWith(d.NEXT_INTERCEPTION_MARKER_PREFIX);(r||s||t.includes(e)||i&&Object.keys(i.groups).includes(e))&&delete n.query[e]}e.url=(0,r.format)(n)}function m(e,t,i){if(!i)return e;for(let r of Object.keys(i.groups)){let n,{optional:s,repeat:a}=i.groups[r],o=`[${a?"...":""}${r}]`;s&&(o=`[${o}]`);let l=t[r];n=Array.isArray(l)?l.map(e=>e&&encodeURIComponent(e)).join("/"):l?encodeURIComponent(l):"",e=e.replaceAll(o,n)}return e}function f(e,t,i,r){let n={};for(let s of Object.keys(t.groups)){let a=e[s];"string"==typeof a?a=(0,c.normalizeRscURL)(a):Array.isArray(a)&&(a=a.map(c.normalizeRscURL));let o=i[s],l=t.groups[s].optional;if((Array.isArray(o)?o.some(e=>Array.isArray(a)?a.some(t=>t.includes(e)):null==a?void 0:a.includes(e)):null==a?void 0:a.includes(o))||void 0===a&&!(l&&r))return{params:{},hasValidParams:!1};l&&(!a||Array.isArray(a)&&1===a.length&&("index"===a[0]||a[0]===`[[...${s}]]`))&&(a=void 0,delete e[s]),a&&"string"==typeof a&&t.groups[s].repeat&&(a=a.split("/")),a&&(n[s]=a)}return{params:n,hasValidParams:!0}}function g({page:e,i18n:t,basePath:i,rewrites:r,pageIsDynamic:c,trailingSlash:d,caseSensitive:g}){let v,y,x;return c&&(v=(0,a.getNamedRouteRegex)(e,{prefixRouteKeys:!1}),x=(y=(0,o.getRouteMatcher)(v))(e)),{handleRewrites:function(a,o){let h={},p=o.pathname,m=r=>{let u=(0,s.getPathMatch)(r.source+(d?"(/)?":""),{removeUnnamedParams:!0,strict:!0,sensitive:!!g});if(!o.pathname)return!1;let m=u(o.pathname);if((r.has||r.missing)&&m){let e=(0,l.matchHas)(a,o.query,r.has,r.missing);e?Object.assign(m,e):m=!1}if(m){let{parsedDestination:s,destQuery:a}=(0,l.prepareDestination)({appendParamsToQuery:!0,destination:r.destination,params:m,query:o.query});if(s.protocol)return!0;if(Object.assign(h,a,m),Object.assign(o.query,s.query),delete s.query,Object.assign(o,s),!(p=o.pathname))return!1;if(i&&(p=p.replace(RegExp(`^${i}`),"")||"/"),t){let e=(0,n.normalizeLocalePath)(p,t.locales);p=e.pathname,o.query.nextInternalLocale=e.detectedLocale||m.nextInternalLocale}if(p===e)return!0;if(c&&y){let e=y(p);if(e)return o.query={...o.query,...e},!0}}return!1};for(let e of r.beforeFiles||[])m(e);if(p!==e){let t=!1;for(let e of r.afterFiles||[])if(t=m(e))break;if(!t&&!(()=>{let t=(0,u.removeTrailingSlash)(p||"");return t===(0,u.removeTrailingSlash)(e)||(null==y?void 0:y(t))})()){for(let e of r.fallback||[])if(t=m(e))break}}return h},defaultRouteRegex:v,dynamicRouteMatcher:y,defaultRouteMatches:x,getParamsFromRouteMatches:function(e){if(!v)return null;let{groups:t,routeKeys:i}=v,r=(0,o.getRouteMatcher)({re:{exec:e=>{let r=Object.fromEntries(new URLSearchParams(e));for(let[e,t]of Object.entries(r)){let i=(0,h.normalizeNextQueryParam)(e);i&&(r[i]=t,delete r[e])}let n={};for(let e of Object.keys(i)){let s=i[e];if(!s)continue;let a=t[s],o=r[e];if(!a.optional&&!o)return null;n[a.pos]=o}return n}},groups:t})(e);return r||null},normalizeDynamicRouteParams:(e,t)=>v&&x?f(e,v,x,t):{params:{},hasValidParams:!1},normalizeVercelUrl:(e,t)=>p(e,t,v),interpolateDynamicPath:(e,t)=>m(e,t,v)}}function v(e,t){return"string"==typeof e[d.NEXT_CACHE_REVALIDATED_TAGS_HEADER]&&e[d.NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER]===t?e[d.NEXT_CACHE_REVALIDATED_TAGS_HEADER].split(","):[]}},6415:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{t.parse=function(t,i){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var n={},s=t.split(r),a=(i||{}).decode||e,o=0;o<s.length;o++){var l=s[o],u=l.indexOf("=");if(!(u<0)){var c=l.substr(0,u).trim(),d=l.substr(++u,l.length).trim();'"'==d[0]&&(d=d.slice(1,-1)),void 0==n[c]&&(n[c]=function(e,t){try{return t(e)}catch(t){return e}}(d,a))}}return n},t.serialize=function(e,t,r){var s=r||{},a=s.encode||i;if("function"!=typeof a)throw TypeError("option encode is invalid");if(!n.test(e))throw TypeError("argument name is invalid");var o=a(t);if(o&&!n.test(o))throw TypeError("argument val is invalid");var l=e+"="+o;if(null!=s.maxAge){var u=s.maxAge-0;if(isNaN(u)||!isFinite(u))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(u)}if(s.domain){if(!n.test(s.domain))throw TypeError("option domain is invalid");l+="; Domain="+s.domain}if(s.path){if(!n.test(s.path))throw TypeError("option path is invalid");l+="; Path="+s.path}if(s.expires){if("function"!=typeof s.expires.toUTCString)throw TypeError("option expires is invalid");l+="; Expires="+s.expires.toUTCString()}if(s.httpOnly&&(l+="; HttpOnly"),s.secure&&(l+="; Secure"),s.sameSite)switch("string"==typeof s.sameSite?s.sameSite.toLowerCase():s.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var e=decodeURIComponent,i=encodeURIComponent,r=/; */,n=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},6533:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Image",{enumerable:!0,get:function(){return b}});let r=i(4985),n=i(740),s=i(687),a=n._(i(3210)),o=r._(i(1215)),l=r._(i(512)),u=i(4953),c=i(2756),d=i(7903);i(148);let h=i(9148),p=r._(i(1933)),m=i(3038),f={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1};function g(e,t,i,r,n,s,a){let o=null==e?void 0:e.src;e&&e["data-loaded-src"]!==o&&(e["data-loaded-src"]=o,("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(e.parentElement&&e.isConnected){if("empty"!==t&&n(!0),null==i?void 0:i.current){let t=new Event("load");Object.defineProperty(t,"target",{writable:!1,value:e});let r=!1,n=!1;i.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>r,isPropagationStopped:()=>n,persist:()=>{},preventDefault:()=>{r=!0,t.preventDefault()},stopPropagation:()=>{n=!0,t.stopPropagation()}})}(null==r?void 0:r.current)&&r.current(e)}}))}function v(e){return a.use?{fetchPriority:e}:{fetchpriority:e}}globalThis.__NEXT_IMAGE_IMPORTED=!0;let y=(0,a.forwardRef)((e,t)=>{let{src:i,srcSet:r,sizes:n,height:o,width:l,decoding:u,className:c,style:d,fetchPriority:h,placeholder:p,loading:f,unoptimized:y,fill:x,onLoadRef:b,onLoadingCompleteRef:w,setBlurComplete:j,setShowAltText:P,sizesInput:E,onLoad:T,onError:S,...A}=e,R=(0,a.useCallback)(e=>{e&&(S&&(e.src=e.src),e.complete&&g(e,p,b,w,j,y,E))},[i,p,b,w,j,S,y,E]),M=(0,m.useMergedRef)(t,R);return(0,s.jsx)("img",{...A,...v(h),loading:f,width:l,height:o,decoding:u,"data-nimg":x?"fill":"1",className:c,style:d,sizes:n,srcSet:r,src:i,ref:M,onLoad:e=>{g(e.currentTarget,p,b,w,j,y,E)},onError:e=>{P(!0),"empty"!==p&&j(!0),S&&S(e)}})});function x(e){let{isAppRouter:t,imgAttributes:i}=e,r={as:"image",imageSrcSet:i.srcSet,imageSizes:i.sizes,crossOrigin:i.crossOrigin,referrerPolicy:i.referrerPolicy,...v(i.fetchPriority)};return t&&o.default.preload?(o.default.preload(i.src,r),null):(0,s.jsx)(l.default,{children:(0,s.jsx)("link",{rel:"preload",href:i.srcSet?void 0:i.src,...r},"__nimg-"+i.src+i.srcSet+i.sizes)})}let b=(0,a.forwardRef)((e,t)=>{let i=(0,a.useContext)(h.RouterContext),r=(0,a.useContext)(d.ImageConfigContext),n=(0,a.useMemo)(()=>{var e;let t=f||r||c.imageConfigDefault,i=[...t.deviceSizes,...t.imageSizes].sort((e,t)=>e-t),n=t.deviceSizes.sort((e,t)=>e-t),s=null==(e=t.qualities)?void 0:e.sort((e,t)=>e-t);return{...t,allSizes:i,deviceSizes:n,qualities:s}},[r]),{onLoad:o,onLoadingComplete:l}=e,m=(0,a.useRef)(o);(0,a.useEffect)(()=>{m.current=o},[o]);let g=(0,a.useRef)(l);(0,a.useEffect)(()=>{g.current=l},[l]);let[v,b]=(0,a.useState)(!1),[w,j]=(0,a.useState)(!1),{props:P,meta:E}=(0,u.getImgProps)(e,{defaultLoader:p.default,imgConf:n,blurComplete:v,showAltText:w});return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(y,{...P,unoptimized:E.unoptimized,placeholder:E.placeholder,fill:E.fill,onLoadRef:m,onLoadingCompleteRef:g,setBlurComplete:b,setShowAltText:j,sizesInput:e.sizes,ref:t}),E.priority?(0,s.jsx)(x,{isAppRouter:!i,imgAttributes:P}):null]})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6759:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseUrl",{enumerable:!0,get:function(){return s}});let r=i(2785),n=i(3736);function s(e){if(e.startsWith("/"))return(0,n.parseRelativeUrl)(e);let t=new URL(e);return{hash:t.hash,hostname:t.hostname,href:t.href,pathname:t.pathname,port:t.port,protocol:t.protocol,query:(0,r.searchParamsToUrlQuery)(t.searchParams),search:t.search}}},7551:()=>{},7755:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return a}});let r=i(3210),n=()=>{},s=()=>{};function a(e){var t;let{headManager:i,reduceComponentsToState:a}=e;function o(){if(i&&i.mountedInstances){let t=r.Children.toArray(Array.from(i.mountedInstances).filter(Boolean));i.updateHead(a(t,e))}}return null==i||null==(t=i.mountedInstances)||t.add(e.children),o(),n(()=>{var t;return null==i||null==(t=i.mountedInstances)||t.add(e.children),()=>{var t;null==i||null==(t=i.mountedInstances)||t.delete(e.children)}}),n(()=>(i&&(i._pendingUpdate=o),()=>{i&&(i._pendingUpdate=o)})),s(()=>(i&&i._pendingUpdate&&(i._pendingUpdate(),i._pendingUpdate=null),()=>{i&&i._pendingUpdate&&(i._pendingUpdate(),i._pendingUpdate=null)})),null}},7903:(e,t,i)=>{"use strict";e.exports=i(4041).vendored.contexts.ImageConfigContext},8034:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getRouteMatcher",{enumerable:!0,get:function(){return n}});let r=i(4827);function n(e){let{re:t,groups:i}=e;return e=>{let n=t.exec(e);if(!n)return!1;let s=e=>{try{return decodeURIComponent(e)}catch(e){throw Object.defineProperty(new r.DecodeError("failed to decode param"),"__NEXT_ERROR_CODE",{value:"E528",enumerable:!1,configurable:!0})}},a={};for(let[e,t]of Object.entries(i)){let i=n[t.pos];void 0!==i&&(t.repeat?a[e]=i.split("/").map(e=>s(e)):a[e]=s(i))}return a}}},8212:(e,t,i)=>{"use strict";function r(e){return function(){let{cookie:t}=e;if(!t)return{};let{parse:r}=i(6415);return r(Array.isArray(t)?t.join("; "):t)}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getCookieParser",{enumerable:!0,get:function(){return r}})},8231:()=>{},8304:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{DEFAULT_METADATA_ROUTE_EXTENSIONS:function(){return o},STATIC_METADATA_IMAGES:function(){return a},getExtensionRegexString:function(){return l},isMetadataPage:function(){return d},isMetadataRoute:function(){return h},isMetadataRouteFile:function(){return u},isStaticMetadataRoute:function(){return c}});let r=i(2958),n=i(4722),s=i(554),a={icon:{filename:"icon",extensions:["ico","jpg","jpeg","png","svg"]},apple:{filename:"apple-icon",extensions:["jpg","jpeg","png"]},favicon:{filename:"favicon",extensions:["ico"]},openGraph:{filename:"opengraph-image",extensions:["jpg","jpeg","png","gif"]},twitter:{filename:"twitter-image",extensions:["jpg","jpeg","png","gif"]}},o=["js","jsx","ts","tsx"],l=(e,t)=>t&&0!==t.length?`(?:\\.(${e.join("|")})|(\\.(${t.join("|")})))`:`(\\.(?:${e.join("|")}))`;function u(e,t,i){let n=(i?"":"?")+"$",s=`\\d?${i?"":"(-\\w{6})?"}`,o=[RegExp(`^[\\\\/]robots${l(t.concat("txt"),null)}${n}`),RegExp(`^[\\\\/]manifest${l(t.concat("webmanifest","json"),null)}${n}`),RegExp("^[\\\\/]favicon\\.ico$"),RegExp(`[\\\\/]sitemap${l(["xml"],t)}${n}`),RegExp(`[\\\\/]${a.icon.filename}${s}${l(a.icon.extensions,t)}${n}`),RegExp(`[\\\\/]${a.apple.filename}${s}${l(a.apple.extensions,t)}${n}`),RegExp(`[\\\\/]${a.openGraph.filename}${s}${l(a.openGraph.extensions,t)}${n}`),RegExp(`[\\\\/]${a.twitter.filename}${s}${l(a.twitter.extensions,t)}${n}`)],u=(0,r.normalizePathSep)(e);return o.some(e=>e.test(u))}function c(e){let t=e.replace(/\/route$/,"");return(0,s.isAppRouteRoute)(e)&&u(t,[],!0)&&"/robots.txt"!==t&&"/manifest.webmanifest"!==t&&!t.endsWith("/sitemap.xml")}function d(e){return!(0,s.isAppRouteRoute)(e)&&u(e,[],!1)}function h(e){let t=(0,n.normalizeAppPath)(e).replace(/^\/?app\//,"").replace("/[__metadata_id__]","").replace(/\/route$/,"");return"/"!==t[0]&&(t="/"+t),(0,s.isAppRouteRoute)(e)&&u(t,[],!1)}},8806:(e,t,i)=>{"use strict";let r;i.r(t),i.d(t,{default:()=>sH});var n,s,a=i(687);function o(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}function l(e){let t=[{},{}];return e?.values.forEach((e,i)=>{t[0][i]=e.get(),t[1][i]=e.getVelocity()}),t}function u(e,t,i,r){if("function"==typeof t){let[n,s]=l(r);t=t(void 0!==i?i:e.custom,n,s)}if("string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t){let[n,s]=l(r);t=t(void 0!==i?i:e.custom,n,s)}return t}function c(e,t,i){let r=e.getProps();return u(r,t,void 0!==i?i:r.custom,e)}function d(e,t){return e?.[t]??e?.default??e}let h=e=>e,p={},m=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],f={value:null,addProjectionMetrics:null};function g(e,t){let i=!1,r=!0,n={delta:0,timestamp:0,isProcessing:!1},s=()=>i=!0,a=m.reduce((e,i)=>(e[i]=function(e,t){let i=new Set,r=new Set,n=!1,s=!1,a=new WeakSet,o={delta:0,timestamp:0,isProcessing:!1},l=0;function u(t){a.has(t)&&(c.schedule(t),e()),l++,t(o)}let c={schedule:(e,t=!1,s=!1)=>{let o=s&&n?i:r;return t&&a.add(e),o.has(e)||o.add(e),e},cancel:e=>{r.delete(e),a.delete(e)},process:e=>{if(o=e,n){s=!0;return}n=!0,[i,r]=[r,i],i.forEach(u),t&&f.value&&f.value.frameloop[t].push(l),l=0,i.clear(),n=!1,s&&(s=!1,c.process(e))}};return c}(s,t?i:void 0),e),{}),{setup:o,read:l,resolveKeyframes:u,preUpdate:c,update:d,preRender:h,render:g,postRender:v}=a,y=()=>{let s=p.useManualTiming?n.timestamp:performance.now();i=!1,p.useManualTiming||(n.delta=r?1e3/60:Math.max(Math.min(s-n.timestamp,40),1)),n.timestamp=s,n.isProcessing=!0,o.process(n),l.process(n),u.process(n),c.process(n),d.process(n),h.process(n),g.process(n),v.process(n),n.isProcessing=!1,i&&t&&(r=!1,e(y))},x=()=>{i=!0,r=!0,n.isProcessing||e(y)};return{schedule:m.reduce((e,t)=>{let r=a[t];return e[t]=(e,t=!1,n=!1)=>(i||x(),r.schedule(e,t,n)),e},{}),cancel:e=>{for(let t=0;t<m.length;t++)a[m[t]].cancel(e)},state:n,steps:a}}let{schedule:v,cancel:y,state:x,steps:b}=g("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:h,!0),w=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],j=new Set(w),P=new Set(["width","height","top","left","right","bottom",...w]);function E(e,t){-1===e.indexOf(t)&&e.push(t)}function T(e,t){let i=e.indexOf(t);i>-1&&e.splice(i,1)}class S{constructor(){this.subscriptions=[]}add(e){return E(this.subscriptions,e),()=>T(this.subscriptions,e)}notify(e,t,i){let r=this.subscriptions.length;if(r)if(1===r)this.subscriptions[0](e,t,i);else for(let n=0;n<r;n++){let r=this.subscriptions[n];r&&r(e,t,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function A(){r=void 0}let R={now:()=>(void 0===r&&R.set(x.isProcessing||p.useManualTiming?x.timestamp:performance.now()),r),set:e=>{r=e,queueMicrotask(A)}},M=e=>!isNaN(parseFloat(e)),N={current:void 0};class C{constructor(e,t={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(e,t=!0)=>{let i=R.now();if(this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(e),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let e of this.dependents)e.dirty();t&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(e),this.owner=t.owner}setCurrent(e){this.current=e,this.updatedAt=R.now(),null===this.canTrackVelocity&&void 0!==e&&(this.canTrackVelocity=M(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new S);let i=this.events[e].add(t);return"change"===e?()=>{i(),v.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e,t=!0){t&&this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e,t)}setWithVelocity(e,t,i){this.set(t),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-i}jump(e,t=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,t&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(e){this.dependents||(this.dependents=new Set),this.dependents.add(e)}removeDependent(e){this.dependents&&this.dependents.delete(e)}get(){return N.current&&N.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var e;let t=R.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return e=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*e:0}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function k(e,t){return new C(e,t)}let _=e=>Array.isArray(e),D=e=>!!(e&&e.getVelocity);function O(e,t){let i=e.getValue("willChange");if(D(i)&&i.add)return i.add(t);if(!i&&p.WillChange){let i=new p.WillChange("auto");e.addValue("willChange",i),i.add(t)}}let V=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),L="data-"+V("framerAppearId"),I=(e,t)=>i=>t(e(i)),F=(...e)=>e.reduce(I),U=(e,t,i)=>i>t?t:i<e?e:i,$=e=>1e3*e,B=e=>e/1e3,z={layout:0,mainThread:0,waapi:0},W=()=>{},q=()=>{},H=e=>t=>"string"==typeof t&&t.startsWith(e),X=H("--"),G=H("var(--"),K=e=>!!G(e)&&Y.test(e.split("/*")[0].trim()),Y=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,Z={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},Q={...Z,transform:e=>U(0,1,e)},J={...Z,default:1},ee=e=>Math.round(1e5*e)/1e5,et=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,ei=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,er=(e,t)=>i=>!!("string"==typeof i&&ei.test(i)&&i.startsWith(e)||t&&null!=i&&Object.prototype.hasOwnProperty.call(i,t)),en=(e,t,i)=>r=>{if("string"!=typeof r)return r;let[n,s,a,o]=r.match(et);return{[e]:parseFloat(n),[t]:parseFloat(s),[i]:parseFloat(a),alpha:void 0!==o?parseFloat(o):1}},es=e=>U(0,255,e),ea={...Z,transform:e=>Math.round(es(e))},eo={test:er("rgb","red"),parse:en("red","green","blue"),transform:({red:e,green:t,blue:i,alpha:r=1})=>"rgba("+ea.transform(e)+", "+ea.transform(t)+", "+ea.transform(i)+", "+ee(Q.transform(r))+")"},el={test:er("#"),parse:function(e){let t="",i="",r="",n="";return e.length>5?(t=e.substring(1,3),i=e.substring(3,5),r=e.substring(5,7),n=e.substring(7,9)):(t=e.substring(1,2),i=e.substring(2,3),r=e.substring(3,4),n=e.substring(4,5),t+=t,i+=i,r+=r,n+=n),{red:parseInt(t,16),green:parseInt(i,16),blue:parseInt(r,16),alpha:n?parseInt(n,16)/255:1}},transform:eo.transform},eu=e=>({test:t=>"string"==typeof t&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}),ec=eu("deg"),ed=eu("%"),eh=eu("px"),ep=eu("vh"),em=eu("vw"),ef={...ed,parse:e=>ed.parse(e)/100,transform:e=>ed.transform(100*e)},eg={test:er("hsl","hue"),parse:en("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:i,alpha:r=1})=>"hsla("+Math.round(e)+", "+ed.transform(ee(t))+", "+ed.transform(ee(i))+", "+ee(Q.transform(r))+")"},ev={test:e=>eo.test(e)||el.test(e)||eg.test(e),parse:e=>eo.test(e)?eo.parse(e):eg.test(e)?eg.parse(e):el.parse(e),transform:e=>"string"==typeof e?e:e.hasOwnProperty("red")?eo.transform(e):eg.transform(e),getAnimatableNone:e=>{let t=ev.parse(e);return t.alpha=0,ev.transform(t)}},ey=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,ex="number",eb="color",ew=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function ej(e){let t=e.toString(),i=[],r={color:[],number:[],var:[]},n=[],s=0,a=t.replace(ew,e=>(ev.test(e)?(r.color.push(s),n.push(eb),i.push(ev.parse(e))):e.startsWith("var(")?(r.var.push(s),n.push("var"),i.push(e)):(r.number.push(s),n.push(ex),i.push(parseFloat(e))),++s,"${}")).split("${}");return{values:i,split:a,indexes:r,types:n}}function eP(e){return ej(e).values}function eE(e){let{split:t,types:i}=ej(e),r=t.length;return e=>{let n="";for(let s=0;s<r;s++)if(n+=t[s],void 0!==e[s]){let t=i[s];t===ex?n+=ee(e[s]):t===eb?n+=ev.transform(e[s]):n+=e[s]}return n}}let eT=e=>"number"==typeof e?0:ev.test(e)?ev.getAnimatableNone(e):e,eS={test:function(e){return isNaN(e)&&"string"==typeof e&&(e.match(et)?.length||0)+(e.match(ey)?.length||0)>0},parse:eP,createTransformer:eE,getAnimatableNone:function(e){let t=eP(e);return eE(e)(t.map(eT))}};function eA(e,t,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?e+(t-e)*6*i:i<.5?t:i<2/3?e+(t-e)*(2/3-i)*6:e}function eR(e,t){return i=>i>0?t:e}let eM=(e,t,i)=>e+(t-e)*i,eN=(e,t,i)=>{let r=e*e,n=i*(t*t-r)+r;return n<0?0:Math.sqrt(n)},eC=[el,eo,eg],ek=e=>eC.find(t=>t.test(e));function e_(e){let t=ek(e);if(W(!!t,`'${e}' is not an animatable color. Use the equivalent color code instead.`),!t)return!1;let i=t.parse(e);return t===eg&&(i=function({hue:e,saturation:t,lightness:i,alpha:r}){e/=360,i/=100;let n=0,s=0,a=0;if(t/=100){let r=i<.5?i*(1+t):i+t-i*t,o=2*i-r;n=eA(o,r,e+1/3),s=eA(o,r,e),a=eA(o,r,e-1/3)}else n=s=a=i;return{red:Math.round(255*n),green:Math.round(255*s),blue:Math.round(255*a),alpha:r}}(i)),i}let eD=(e,t)=>{let i=e_(e),r=e_(t);if(!i||!r)return eR(e,t);let n={...i};return e=>(n.red=eN(i.red,r.red,e),n.green=eN(i.green,r.green,e),n.blue=eN(i.blue,r.blue,e),n.alpha=eM(i.alpha,r.alpha,e),eo.transform(n))},eO=new Set(["none","hidden"]);function eV(e,t){return i=>eM(e,t,i)}function eL(e){return"number"==typeof e?eV:"string"==typeof e?K(e)?eR:ev.test(e)?eD:eU:Array.isArray(e)?eI:"object"==typeof e?ev.test(e)?eD:eF:eR}function eI(e,t){let i=[...e],r=i.length,n=e.map((e,i)=>eL(e)(e,t[i]));return e=>{for(let t=0;t<r;t++)i[t]=n[t](e);return i}}function eF(e,t){let i={...e,...t},r={};for(let n in i)void 0!==e[n]&&void 0!==t[n]&&(r[n]=eL(e[n])(e[n],t[n]));return e=>{for(let t in r)i[t]=r[t](e);return i}}let eU=(e,t)=>{let i=eS.createTransformer(t),r=ej(e),n=ej(t);return r.indexes.var.length===n.indexes.var.length&&r.indexes.color.length===n.indexes.color.length&&r.indexes.number.length>=n.indexes.number.length?eO.has(e)&&!n.values.length||eO.has(t)&&!r.values.length?function(e,t){return eO.has(e)?i=>i<=0?e:t:i=>i>=1?t:e}(e,t):F(eI(function(e,t){let i=[],r={color:0,var:0,number:0};for(let n=0;n<t.values.length;n++){let s=t.types[n],a=e.indexes[s][r[s]],o=e.values[a]??0;i[n]=o,r[s]++}return i}(r,n),n.values),i):(W(!0,`Complex values '${e}' and '${t}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),eR(e,t))};function e$(e,t,i){return"number"==typeof e&&"number"==typeof t&&"number"==typeof i?eM(e,t,i):eL(e)(e,t)}let eB=e=>{let t=({timestamp:t})=>e(t);return{start:(e=!0)=>v.update(t,e),stop:()=>y(t),now:()=>x.isProcessing?x.timestamp:R.now()}},ez=(e,t,i=10)=>{let r="",n=Math.max(Math.round(t/i),2);for(let t=0;t<n;t++)r+=Math.round(1e4*e(t/(n-1)))/1e4+", ";return`linear(${r.substring(0,r.length-2)})`};function eW(e){let t=0,i=e.next(t);for(;!i.done&&t<2e4;)t+=50,i=e.next(t);return t>=2e4?1/0:t}function eq(e,t,i){var r,n;let s=Math.max(t-5,0);return r=i-e(s),(n=t-s)?1e3/n*r:0}let eH={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function eX(e,t){return e*Math.sqrt(1-t*t)}let eG=["duration","bounce"],eK=["stiffness","damping","mass"];function eY(e,t){return t.some(t=>void 0!==e[t])}function eZ(e=eH.visualDuration,t=eH.bounce){let i,r="object"!=typeof e?{visualDuration:e,keyframes:[0,1],bounce:t}:e,{restSpeed:n,restDelta:s}=r,a=r.keyframes[0],o=r.keyframes[r.keyframes.length-1],l={done:!1,value:a},{stiffness:u,damping:c,mass:d,duration:h,velocity:p,isResolvedFromDuration:m}=function(e){let t={velocity:eH.velocity,stiffness:eH.stiffness,damping:eH.damping,mass:eH.mass,isResolvedFromDuration:!1,...e};if(!eY(e,eK)&&eY(e,eG))if(e.visualDuration){let i=2*Math.PI/(1.2*e.visualDuration),r=i*i,n=2*U(.05,1,1-(e.bounce||0))*Math.sqrt(r);t={...t,mass:eH.mass,stiffness:r,damping:n}}else{let i=function({duration:e=eH.duration,bounce:t=eH.bounce,velocity:i=eH.velocity,mass:r=eH.mass}){let n,s;W(e<=$(eH.maxDuration),"Spring duration must be 10 seconds or less");let a=1-t;a=U(eH.minDamping,eH.maxDamping,a),e=U(eH.minDuration,eH.maxDuration,B(e)),a<1?(n=t=>{let r=t*a,n=r*e;return .001-(r-i)/eX(t,a)*Math.exp(-n)},s=t=>{let r=t*a*e,s=Math.pow(a,2)*Math.pow(t,2)*e,o=Math.exp(-r),l=eX(Math.pow(t,2),a);return(r*i+i-s)*o*(-n(t)+.001>0?-1:1)/l}):(n=t=>-.001+Math.exp(-t*e)*((t-i)*e+1),s=t=>e*e*(i-t)*Math.exp(-t*e));let o=function(e,t,i){let r=i;for(let i=1;i<12;i++)r-=e(r)/t(r);return r}(n,s,5/e);if(e=$(e),isNaN(o))return{stiffness:eH.stiffness,damping:eH.damping,duration:e};{let t=Math.pow(o,2)*r;return{stiffness:t,damping:2*a*Math.sqrt(r*t),duration:e}}}(e);(t={...t,...i,mass:eH.mass}).isResolvedFromDuration=!0}return t}({...r,velocity:-B(r.velocity||0)}),f=p||0,g=c/(2*Math.sqrt(u*d)),v=o-a,y=B(Math.sqrt(u/d)),x=5>Math.abs(v);if(n||(n=x?eH.restSpeed.granular:eH.restSpeed.default),s||(s=x?eH.restDelta.granular:eH.restDelta.default),g<1){let e=eX(y,g);i=t=>o-Math.exp(-g*y*t)*((f+g*y*v)/e*Math.sin(e*t)+v*Math.cos(e*t))}else if(1===g)i=e=>o-Math.exp(-y*e)*(v+(f+y*v)*e);else{let e=y*Math.sqrt(g*g-1);i=t=>{let i=Math.exp(-g*y*t),r=Math.min(e*t,300);return o-i*((f+g*y*v)*Math.sinh(r)+e*v*Math.cosh(r))/e}}let b={calculatedDuration:m&&h||null,next:e=>{let t=i(e);if(m)l.done=e>=h;else{let r=0===e?f:0;g<1&&(r=0===e?$(f):eq(i,e,t));let a=Math.abs(o-t)<=s;l.done=Math.abs(r)<=n&&a}return l.value=l.done?o:t,l},toString:()=>{let e=Math.min(eW(b),2e4),t=ez(t=>b.next(e*t).value,e,30);return e+"ms "+t},toTransition:()=>{}};return b}function eQ({keyframes:e,velocity:t=0,power:i=.8,timeConstant:r=325,bounceDamping:n=10,bounceStiffness:s=500,modifyTarget:a,min:o,max:l,restDelta:u=.5,restSpeed:c}){let d,h,p=e[0],m={done:!1,value:p},f=e=>void 0!==o&&e<o||void 0!==l&&e>l,g=e=>void 0===o?l:void 0===l||Math.abs(o-e)<Math.abs(l-e)?o:l,v=i*t,y=p+v,x=void 0===a?y:a(y);x!==y&&(v=x-p);let b=e=>-v*Math.exp(-e/r),w=e=>x+b(e),j=e=>{let t=b(e),i=w(e);m.done=Math.abs(t)<=u,m.value=m.done?x:i},P=e=>{f(m.value)&&(d=e,h=eZ({keyframes:[m.value,g(m.value)],velocity:eq(w,e,m.value),damping:n,stiffness:s,restDelta:u,restSpeed:c}))};return P(0),{calculatedDuration:null,next:e=>{let t=!1;return(h||void 0!==d||(t=!0,j(e),P(e)),void 0!==d&&e>=d)?h.next(e-d):(t||j(e),m)}}}eZ.applyToOptions=e=>{let t=function(e,t=100,i){let r=i({...e,keyframes:[0,t]}),n=Math.min(eW(r),2e4);return{type:"keyframes",ease:e=>r.next(n*e).value/t,duration:B(n)}}(e,100,eZ);return e.ease=t.ease,e.duration=$(t.duration),e.type="keyframes",e};let eJ=(e,t,i)=>(((1-3*i+3*t)*e+(3*i-6*t))*e+3*t)*e;function e0(e,t,i,r){if(e===t&&i===r)return h;let n=t=>(function(e,t,i,r,n){let s,a,o=0;do(s=eJ(a=t+(i-t)/2,r,n)-e)>0?i=a:t=a;while(Math.abs(s)>1e-7&&++o<12);return a})(t,0,1,e,i);return e=>0===e||1===e?e:eJ(n(e),t,r)}let e1=e0(.42,0,1,1),e2=e0(0,0,.58,1),e3=e0(.42,0,.58,1),e4=e=>Array.isArray(e)&&"number"!=typeof e[0],e5=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,e6=e=>t=>1-e(1-t),e8=e0(.33,1.53,.69,.99),e9=e6(e8),e7=e5(e9),te=e=>(e*=2)<1?.5*e9(e):.5*(2-Math.pow(2,-10*(e-1))),tt=e=>1-Math.sin(Math.acos(e)),ti=e6(tt),tr=e5(tt),tn=e=>Array.isArray(e)&&"number"==typeof e[0],ts={linear:h,easeIn:e1,easeInOut:e3,easeOut:e2,circIn:tt,circInOut:tr,circOut:ti,backIn:e9,backInOut:e7,backOut:e8,anticipate:te},ta=e=>"string"==typeof e,to=e=>{if(tn(e)){q(4===e.length,"Cubic bezier arrays must contain four numerical values.");let[t,i,r,n]=e;return e0(t,i,r,n)}return ta(e)?(q(void 0!==ts[e],`Invalid easing type '${e}'`),ts[e]):e},tl=(e,t,i)=>{let r=t-e;return 0===r?1:(i-e)/r};function tu({duration:e=300,keyframes:t,times:i,ease:r="easeInOut"}){var n;let s=e4(r)?r.map(to):to(r),a={done:!1,value:t[0]},o=function(e,t,{clamp:i=!0,ease:r,mixer:n}={}){let s=e.length;if(q(s===t.length,"Both input and output ranges must be the same length"),1===s)return()=>t[0];if(2===s&&t[0]===t[1])return()=>t[1];let a=e[0]===e[1];e[0]>e[s-1]&&(e=[...e].reverse(),t=[...t].reverse());let o=function(e,t,i){let r=[],n=i||p.mix||e$,s=e.length-1;for(let i=0;i<s;i++){let s=n(e[i],e[i+1]);t&&(s=F(Array.isArray(t)?t[i]||h:t,s)),r.push(s)}return r}(t,r,n),l=o.length,u=i=>{if(a&&i<e[0])return t[0];let r=0;if(l>1)for(;r<e.length-2&&!(i<e[r+1]);r++);let n=tl(e[r],e[r+1],i);return o[r](n)};return i?t=>u(U(e[0],e[s-1],t)):u}((n=i&&i.length===t.length?i:function(e){let t=[0];return!function(e,t){let i=e[e.length-1];for(let r=1;r<=t;r++){let n=tl(0,t,r);e.push(eM(i,1,n))}}(t,e.length-1),t}(t),n.map(t=>t*e)),t,{ease:Array.isArray(s)?s:t.map(()=>s||e3).splice(0,t.length-1)});return{calculatedDuration:e,next:t=>(a.value=o(t),a.done=t>=e,a)}}let tc=e=>null!==e;function td(e,{repeat:t,repeatType:i="loop"},r,n=1){let s=e.filter(tc),a=n<0||t&&"loop"!==i&&t%2==1?0:s.length-1;return a&&void 0!==r?r:s[a]}let th={decay:eQ,inertia:eQ,tween:tu,keyframes:tu,spring:eZ};function tp(e){"string"==typeof e.type&&(e.type=th[e.type])}class tm{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(e=>{this.resolve=e})}notifyFinished(){this.resolve()}then(e,t){return this.finished.then(e,t)}}let tf=e=>e/100;class tg extends tm{constructor(e){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:e}=this.options;e&&e.updatedAt!==R.now()&&this.tick(R.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},z.mainThread++,this.options=e,this.initAnimation(),this.play(),!1===e.autoplay&&this.pause()}initAnimation(){let{options:e}=this;tp(e);let{type:t=tu,repeat:i=0,repeatDelay:r=0,repeatType:n,velocity:s=0}=e,{keyframes:a}=e,o=t||tu;o!==tu&&"number"!=typeof a[0]&&(this.mixKeyframes=F(tf,e$(a[0],a[1])),a=[0,100]);let l=o({...e,keyframes:a});"mirror"===n&&(this.mirroredGenerator=o({...e,keyframes:[...a].reverse(),velocity:-s})),null===l.calculatedDuration&&(l.calculatedDuration=eW(l));let{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+r,this.totalDuration=this.resolvedDuration*(i+1)-r,this.generator=l}updateTime(e){let t=Math.round(e-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=t}tick(e,t=!1){let{generator:i,totalDuration:r,mixKeyframes:n,mirroredGenerator:s,resolvedDuration:a,calculatedDuration:o}=this;if(null===this.startTime)return i.next(0);let{delay:l=0,keyframes:u,repeat:c,repeatType:d,repeatDelay:h,type:p,onUpdate:m,finalKeyframe:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-r/this.speed,this.startTime)),t?this.currentTime=e:this.updateTime(e);let g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),v=this.playbackSpeed>=0?g<0:g>r;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=r);let y=this.currentTime,x=i;if(c){let e=Math.min(this.currentTime,r)/a,t=Math.floor(e),i=e%1;!i&&e>=1&&(i=1),1===i&&t--,(t=Math.min(t,c+1))%2&&("reverse"===d?(i=1-i,h&&(i-=h/a)):"mirror"===d&&(x=s)),y=U(0,1,i)*a}let b=v?{done:!1,value:u[0]}:x.next(y);n&&(b.value=n(b.value));let{done:w}=b;v||null===o||(w=this.playbackSpeed>=0?this.currentTime>=r:this.currentTime<=0);let j=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return j&&p!==eQ&&(b.value=td(u,this.options,f,this.speed)),m&&m(b.value),j&&this.finish(),b}then(e,t){return this.finished.then(e,t)}get duration(){return B(this.calculatedDuration)}get time(){return B(this.currentTime)}set time(e){e=$(e),this.currentTime=e,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(e){this.updateTime(R.now());let t=this.playbackSpeed!==e;this.playbackSpeed=e,t&&(this.time=B(this.currentTime))}play(){if(this.isStopped)return;let{driver:e=eB,startTime:t}=this.options;this.driver||(this.driver=e(e=>this.tick(e))),this.options.onPlay?.();let i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=t??i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(R.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,z.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}attachTimeline(e){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),e.observe(this)}}let tv=e=>180*e/Math.PI,ty=e=>tb(tv(Math.atan2(e[1],e[0]))),tx={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:e=>(Math.abs(e[0])+Math.abs(e[3]))/2,rotate:ty,rotateZ:ty,skewX:e=>tv(Math.atan(e[1])),skewY:e=>tv(Math.atan(e[2])),skew:e=>(Math.abs(e[1])+Math.abs(e[2]))/2},tb=e=>((e%=360)<0&&(e+=360),e),tw=e=>Math.sqrt(e[0]*e[0]+e[1]*e[1]),tj=e=>Math.sqrt(e[4]*e[4]+e[5]*e[5]),tP={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:tw,scaleY:tj,scale:e=>(tw(e)+tj(e))/2,rotateX:e=>tb(tv(Math.atan2(e[6],e[5]))),rotateY:e=>tb(tv(Math.atan2(-e[2],e[0]))),rotateZ:ty,rotate:ty,skewX:e=>tv(Math.atan(e[4])),skewY:e=>tv(Math.atan(e[1])),skew:e=>(Math.abs(e[1])+Math.abs(e[4]))/2};function tE(e){return+!!e.includes("scale")}function tT(e,t){let i,r;if(!e||"none"===e)return tE(t);let n=e.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(n)i=tP,r=n;else{let t=e.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=tx,r=t}if(!r)return tE(t);let s=i[t],a=r[1].split(",").map(tA);return"function"==typeof s?s(a):a[s]}let tS=(e,t)=>{let{transform:i="none"}=getComputedStyle(e);return tT(i,t)};function tA(e){return parseFloat(e.trim())}let tR=e=>e===Z||e===eh,tM=new Set(["x","y","z"]),tN=w.filter(e=>!tM.has(e)),tC={width:({x:e},{paddingLeft:t="0",paddingRight:i="0"})=>e.max-e.min-parseFloat(t)-parseFloat(i),height:({y:e},{paddingTop:t="0",paddingBottom:i="0"})=>e.max-e.min-parseFloat(t)-parseFloat(i),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:(e,{transform:t})=>tT(t,"x"),y:(e,{transform:t})=>tT(t,"y")};tC.translateX=tC.x,tC.translateY=tC.y;let tk=new Set,t_=!1,tD=!1,tO=!1;function tV(){if(tD){let e=Array.from(tk).filter(e=>e.needsMeasurement),t=new Set(e.map(e=>e.element)),i=new Map;t.forEach(e=>{let t=function(e){let t=[];return tN.forEach(i=>{let r=e.getValue(i);void 0!==r&&(t.push([i,r.get()]),r.set(+!!i.startsWith("scale")))}),t}(e);t.length&&(i.set(e,t),e.render())}),e.forEach(e=>e.measureInitialState()),t.forEach(e=>{e.render();let t=i.get(e);t&&t.forEach(([t,i])=>{e.getValue(t)?.set(i)})}),e.forEach(e=>e.measureEndState()),e.forEach(e=>{void 0!==e.suspendedScrollY&&window.scrollTo(0,e.suspendedScrollY)})}tD=!1,t_=!1,tk.forEach(e=>e.complete(tO)),tk.clear()}function tL(){tk.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(tD=!0)})}class tI{constructor(e,t,i,r,n,s=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...e],this.onComplete=t,this.name=i,this.motionValue=r,this.element=n,this.isAsync=s}scheduleResolve(){this.state="scheduled",this.isAsync?(tk.add(this),t_||(t_=!0,v.read(tL),v.resolveKeyframes(tV))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:e,name:t,element:i,motionValue:r}=this;if(null===e[0]){let n=r?.get(),s=e[e.length-1];if(void 0!==n)e[0]=n;else if(i&&t){let r=i.readValue(t,s);null!=r&&(e[0]=r)}void 0===e[0]&&(e[0]=s),r&&void 0===n&&r.set(e[0])}for(let t=1;t<e.length;t++)e[t]??(e[t]=e[t-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(e=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,e),tk.delete(this)}cancel(){"scheduled"===this.state&&(tk.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let tF=e=>e.startsWith("--");function tU(e){let t;return()=>(void 0===t&&(t=e()),t)}let t$=tU(()=>void 0!==window.ScrollTimeline),tB={},tz=function(e,t){let i=tU(e);return()=>tB[t]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(e){return!1}return!0},"linearEasing"),tW=([e,t,i,r])=>`cubic-bezier(${e}, ${t}, ${i}, ${r})`,tq={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:tW([0,.65,.55,1]),circOut:tW([.55,0,1,.45]),backIn:tW([.31,.01,.66,-.59]),backOut:tW([.33,1.53,.69,.99])};function tH(e){return"function"==typeof e&&"applyToOptions"in e}class tX extends tm{constructor(e){if(super(),this.finishedTime=null,this.isStopped=!1,!e)return;let{element:t,name:i,keyframes:r,pseudoElement:n,allowFlatten:s=!1,finalKeyframe:a,onComplete:o}=e;this.isPseudoElement=!!n,this.allowFlatten=s,this.options=e,q("string"!=typeof e.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let l=function({type:e,...t}){return tH(e)&&tz()?e.applyToOptions(t):(t.duration??(t.duration=300),t.ease??(t.ease="easeOut"),t)}(e);this.animation=function(e,t,i,{delay:r=0,duration:n=300,repeat:s=0,repeatType:a="loop",ease:o="easeOut",times:l}={},u){let c={[t]:i};l&&(c.offset=l);let d=function e(t,i){if(t)return"function"==typeof t?tz()?ez(t,i):"ease-out":tn(t)?tW(t):Array.isArray(t)?t.map(t=>e(t,i)||tq.easeOut):tq[t]}(o,n);Array.isArray(d)&&(c.easing=d),f.value&&z.waapi++;let h={delay:r,duration:n,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:s+1,direction:"reverse"===a?"alternate":"normal"};u&&(h.pseudoElement=u);let p=e.animate(c,h);return f.value&&p.finished.finally(()=>{z.waapi--}),p}(t,i,r,l,n),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!n){let e=td(r,this.options,a,this.speed);this.updateMotionValue?this.updateMotionValue(e):function(e,t,i){tF(t)?e.style.setProperty(t,i):e.style[t]=i}(t,i,e),this.animation.cancel()}o?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(e){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:e}=this;"idle"!==e&&"finished"!==e&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return B(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return B(Number(this.animation.currentTime)||0)}set time(e){this.finishedTime=null,this.animation.currentTime=$(e)}get speed(){return this.animation.playbackRate}set speed(e){e<0&&(this.finishedTime=null),this.animation.playbackRate=e}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(e){this.animation.startTime=e}attachTimeline({timeline:e,observe:t}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,e&&t$())?(this.animation.timeline=e,h):t(this)}}let tG={anticipate:te,backInOut:e7,circInOut:tr};class tK extends tX{constructor(e){!function(e){"string"==typeof e.ease&&e.ease in tG&&(e.ease=tG[e.ease])}(e),tp(e),super(e),e.startTime&&(this.startTime=e.startTime),this.options=e}updateMotionValue(e){let{motionValue:t,onUpdate:i,onComplete:r,element:n,...s}=this.options;if(!t)return;if(void 0!==e)return void t.set(e);let a=new tg({...s,autoplay:!1}),o=$(this.finishedTime??this.time);t.setWithVelocity(a.sample(o-10).value,a.sample(o).value,10),a.stop()}}let tY=(e,t)=>"zIndex"!==t&&!!("number"==typeof e||Array.isArray(e)||"string"==typeof e&&(eS.test(e)||"0"===e)&&!e.startsWith("url("));function tZ(e){return"object"==typeof e&&null!==e}function tQ(e){return tZ(e)&&"offsetHeight"in e}let tJ=new Set(["opacity","clipPath","filter","transform"]),t0=tU(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class t1 extends tm{constructor({autoplay:e=!0,delay:t=0,type:i="keyframes",repeat:r=0,repeatDelay:n=0,repeatType:s="loop",keyframes:a,name:o,motionValue:l,element:u,...c}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=R.now();let d={autoplay:e,delay:t,type:i,repeat:r,repeatDelay:n,repeatType:s,name:o,motionValue:l,element:u,...c},h=u?.KeyframeResolver||tI;this.keyframeResolver=new h(a,(e,t,i)=>this.onKeyframesResolved(e,t,d,!i),o,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(e,t,i,r){this.keyframeResolver=void 0;let{name:n,type:s,velocity:a,delay:o,isHandoff:l,onUpdate:u}=i;this.resolvedAt=R.now(),!function(e,t,i,r){let n=e[0];if(null===n)return!1;if("display"===t||"visibility"===t)return!0;let s=e[e.length-1],a=tY(n,t),o=tY(s,t);return W(a===o,`You are trying to animate ${t} from "${n}" to "${s}". ${n} is not an animatable value - to enable this animation set ${n} to a value animatable to ${s} via the \`style\` property.`),!!a&&!!o&&(function(e){let t=e[0];if(1===e.length)return!0;for(let i=0;i<e.length;i++)if(e[i]!==t)return!0}(e)||("spring"===i||tH(i))&&r)}(e,n,s,a)&&((p.instantAnimations||!o)&&u?.(td(e,i,t)),e[0]=e[e.length-1],i.duration=0,i.repeat=0);let c={startTime:r?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:t,...i,keyframes:e},d=!l&&function(e){let{motionValue:t,name:i,repeatDelay:r,repeatType:n,damping:s,type:a}=e;if(!tQ(t?.owner?.current))return!1;let{onUpdate:o,transformTemplate:l}=t.owner.getProps();return t0()&&i&&tJ.has(i)&&("transform"!==i||!l)&&!o&&!r&&"mirror"!==n&&0!==s&&"inertia"!==a}(c)?new tK({...c,element:c.motionValue.owner.current}):new tg(c);d.finished.then(()=>this.notifyFinished()).catch(h),this.pendingTimeline&&(this.stopTimeline=d.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=d}get finished(){return this._animation?this.animation.finished:this._finished}then(e,t){return this.finished.finally(e).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),tO=!0,tL(),tV(),tO=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(e){this.animation.time=e}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(e){this.animation.speed=e}get startTime(){return this.animation.startTime}attachTimeline(e){return this._animation?this.stopTimeline=this.animation.attachTimeline(e):this.pendingTimeline=e,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let t2=e=>null!==e,t3={type:"spring",stiffness:500,damping:25,restSpeed:10},t4=e=>({type:"spring",stiffness:550,damping:0===e?2*Math.sqrt(550):30,restSpeed:10}),t5={type:"keyframes",duration:.8},t6={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},t8=(e,{keyframes:t})=>t.length>2?t5:j.has(e)?e.startsWith("scale")?t4(t[1]):t3:t6,t9=(e,t,i,r={},n,s)=>a=>{let o=d(r,e)||{},l=o.delay||r.delay||0,{elapsed:u=0}=r;u-=$(l);let c={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:t.getVelocity(),...o,delay:-u,onUpdate:e=>{t.set(e),o.onUpdate&&o.onUpdate(e)},onComplete:()=>{a(),o.onComplete&&o.onComplete()},name:e,motionValue:t,element:s?void 0:n};!function({when:e,delay:t,delayChildren:i,staggerChildren:r,staggerDirection:n,repeat:s,repeatType:a,repeatDelay:o,from:l,elapsed:u,...c}){return!!Object.keys(c).length}(o)&&Object.assign(c,t8(e,c)),c.duration&&(c.duration=$(c.duration)),c.repeatDelay&&(c.repeatDelay=$(c.repeatDelay)),void 0!==c.from&&(c.keyframes[0]=c.from);let h=!1;if(!1!==c.type&&(0!==c.duration||c.repeatDelay)||(c.duration=0,0===c.delay&&(h=!0)),(p.instantAnimations||p.skipAnimations)&&(h=!0,c.duration=0,c.delay=0),c.allowFlatten=!o.type&&!o.ease,h&&!s&&void 0!==t.get()){let e=function(e,{repeat:t,repeatType:i="loop"},r){let n=e.filter(t2),s=t&&"loop"!==i&&t%2==1?0:n.length-1;return n[s]}(c.keyframes,o);if(void 0!==e)return void v.update(()=>{c.onUpdate(e),c.onComplete()})}return o.isSync?new tg(c):new t1(c)};function t7(e,t,{delay:i=0,transitionOverride:r,type:n}={}){let{transition:s=e.getDefaultTransition(),transitionEnd:a,...o}=t;r&&(s=r);let l=[],u=n&&e.animationState&&e.animationState.getState()[n];for(let t in o){let r=e.getValue(t,e.latestValues[t]??null),n=o[t];if(void 0===n||u&&function({protectedKeys:e,needsAnimating:t},i){let r=e.hasOwnProperty(i)&&!0!==t[i];return t[i]=!1,r}(u,t))continue;let a={delay:i,...d(s||{},t)},c=r.get();if(void 0!==c&&!r.isAnimating&&!Array.isArray(n)&&n===c&&!a.velocity)continue;let h=!1;if(window.MotionHandoffAnimation){let i=e.props[L];if(i){let e=window.MotionHandoffAnimation(i,t,v);null!==e&&(a.startTime=e,h=!0)}}O(e,t),r.start(t9(t,r,n,e.shouldReduceMotion&&P.has(t)?{type:!1}:a,e,h));let p=r.animation;p&&l.push(p)}return a&&Promise.all(l).then(()=>{v.update(()=>{a&&function(e,t){let{transitionEnd:i={},transition:r={},...n}=c(e,t)||{};for(let t in n={...n,...i}){var s;let i=_(s=n[t])?s[s.length-1]||0:s;e.hasValue(t)?e.getValue(t).set(i):e.addValue(t,k(i))}}(e,a)})}),l}function ie(e,t,i={}){let r=c(e,t,"exit"===i.type?e.presenceContext?.custom:void 0),{transition:n=e.getDefaultTransition()||{}}=r||{};i.transitionOverride&&(n=i.transitionOverride);let s=r?()=>Promise.all(t7(e,r,i)):()=>Promise.resolve(),a=e.variantChildren&&e.variantChildren.size?(r=0)=>{let{delayChildren:s=0,staggerChildren:a,staggerDirection:o}=n;return function(e,t,i=0,r=0,n=1,s){let a=[],o=(e.variantChildren.size-1)*r,l=1===n?(e=0)=>e*r:(e=0)=>o-e*r;return Array.from(e.variantChildren).sort(it).forEach((e,r)=>{e.notify("AnimationStart",t),a.push(ie(e,t,{...s,delay:i+l(r)}).then(()=>e.notify("AnimationComplete",t)))}),Promise.all(a)}(e,t,s+r,a,o,i)}:()=>Promise.resolve(),{when:o}=n;if(!o)return Promise.all([s(),a(i.delay)]);{let[e,t]="beforeChildren"===o?[s,a]:[a,s];return e().then(()=>t())}}function it(e,t){return e.sortNodePosition(t)}function ii(e,t){if(!Array.isArray(t))return!1;let i=t.length;if(i!==e.length)return!1;for(let r=0;r<i;r++)if(t[r]!==e[r])return!1;return!0}function ir(e){return"string"==typeof e||Array.isArray(e)}let is=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],ia=["initial",...is],io=ia.length,il=[...is].reverse(),iu=is.length;function ic(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function id(){return{animate:ic(!0),whileInView:ic(),whileHover:ic(),whileTap:ic(),whileDrag:ic(),whileFocus:ic(),exit:ic()}}class ih{constructor(e){this.isMounted=!1,this.node=e}update(){}}class ip extends ih{constructor(e){super(e),e.animationState||(e.animationState=function(e){let t=t=>Promise.all(t.map(({animation:t,options:i})=>(function(e,t,i={}){let r;if(e.notify("AnimationStart",t),Array.isArray(t))r=Promise.all(t.map(t=>ie(e,t,i)));else if("string"==typeof t)r=ie(e,t,i);else{let n="function"==typeof t?c(e,t,i.custom):t;r=Promise.all(t7(e,n,i))}return r.then(()=>{e.notify("AnimationComplete",t)})})(e,t,i))),i=id(),r=!0,n=t=>(i,r)=>{let n=c(e,r,"exit"===t?e.presenceContext?.custom:void 0);if(n){let{transition:e,transitionEnd:t,...r}=n;i={...i,...r,...t}}return i};function s(s){let{props:a}=e,l=function e(t){if(!t)return;if(!t.isControllingVariants){let i=t.parent&&e(t.parent)||{};return void 0!==t.props.initial&&(i.initial=t.props.initial),i}let i={};for(let e=0;e<io;e++){let r=ia[e],n=t.props[r];(ir(n)||!1===n)&&(i[r]=n)}return i}(e.parent)||{},u=[],d=new Set,h={},p=1/0;for(let t=0;t<iu;t++){var m,f;let c=il[t],g=i[c],v=void 0!==a[c]?a[c]:l[c],y=ir(v),x=c===s?g.isActive:null;!1===x&&(p=t);let b=v===l[c]&&v!==a[c]&&y;if(b&&r&&e.manuallyAnimateOnMount&&(b=!1),g.protectedKeys={...h},!g.isActive&&null===x||!v&&!g.prevProp||o(v)||"boolean"==typeof v)continue;let w=(m=g.prevProp,"string"==typeof(f=v)?f!==m:!!Array.isArray(f)&&!ii(f,m)),j=w||c===s&&g.isActive&&!b&&y||t>p&&y,P=!1,E=Array.isArray(v)?v:[v],T=E.reduce(n(c),{});!1===x&&(T={});let{prevResolvedValues:S={}}=g,A={...S,...T},R=t=>{j=!0,d.has(t)&&(P=!0,d.delete(t)),g.needsAnimating[t]=!0;let i=e.getValue(t);i&&(i.liveStyle=!1)};for(let e in A){let t=T[e],i=S[e];if(h.hasOwnProperty(e))continue;let r=!1;(_(t)&&_(i)?ii(t,i):t===i)?void 0!==t&&d.has(e)?R(e):g.protectedKeys[e]=!0:null!=t?R(e):d.add(e)}g.prevProp=v,g.prevResolvedValues=T,g.isActive&&(h={...h,...T}),r&&e.blockInitialAnimation&&(j=!1);let M=!(b&&w)||P;j&&M&&u.push(...E.map(e=>({animation:e,options:{type:c}})))}if(d.size){let t={};if("boolean"!=typeof a.initial){let i=c(e,Array.isArray(a.initial)?a.initial[0]:a.initial);i&&i.transition&&(t.transition=i.transition)}d.forEach(i=>{let r=e.getBaseTarget(i),n=e.getValue(i);n&&(n.liveStyle=!0),t[i]=r??null}),u.push({animation:t})}let g=!!u.length;return r&&(!1===a.initial||a.initial===a.animate)&&!e.manuallyAnimateOnMount&&(g=!1),r=!1,g?t(u):Promise.resolve()}return{animateChanges:s,setActive:function(t,r){if(i[t].isActive===r)return Promise.resolve();e.variantChildren?.forEach(e=>e.animationState?.setActive(t,r)),i[t].isActive=r;let n=s(t);for(let e in i)i[e].protectedKeys={};return n},setAnimateFunction:function(i){t=i(e)},getState:()=>i,reset:()=>{i=id(),r=!0}}}(e))}updateAnimationControlsSubscription(){let{animate:e}=this.node.getProps();o(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let im=0;class ig extends ih{constructor(){super(...arguments),this.id=im++}update(){if(!this.node.presenceContext)return;let{isPresent:e,onExitComplete:t}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===i)return;let r=this.node.animationState.setActive("exit",!e);t&&!e&&r.then(()=>{t(this.id)})}mount(){let{register:e,onExitComplete:t}=this.node.presenceContext||{};t&&t(this.id),e&&(this.unmount=e(this.id))}unmount(){}}let iv={x:!1,y:!1};function iy(e,t,i,r={passive:!0}){return e.addEventListener(t,i,r),()=>e.removeEventListener(t,i)}let ix=e=>"mouse"===e.pointerType?"number"!=typeof e.button||e.button<=0:!1!==e.isPrimary;function ib(e){return{point:{x:e.pageX,y:e.pageY}}}let iw=e=>t=>ix(t)&&e(t,ib(t));function ij(e,t,i,r){return iy(e,t,iw(i),r)}function iP({top:e,left:t,right:i,bottom:r}){return{x:{min:t,max:i},y:{min:e,max:r}}}function iE(e){return e.max-e.min}function iT(e,t,i,r=.5){e.origin=r,e.originPoint=eM(t.min,t.max,e.origin),e.scale=iE(i)/iE(t),e.translate=eM(i.min,i.max,e.origin)-e.originPoint,(e.scale>=.9999&&e.scale<=1.0001||isNaN(e.scale))&&(e.scale=1),(e.translate>=-.01&&e.translate<=.01||isNaN(e.translate))&&(e.translate=0)}function iS(e,t,i,r){iT(e.x,t.x,i.x,r?r.originX:void 0),iT(e.y,t.y,i.y,r?r.originY:void 0)}function iA(e,t,i){e.min=i.min+t.min,e.max=e.min+iE(t)}function iR(e,t,i){e.min=t.min-i.min,e.max=e.min+iE(t)}function iM(e,t,i){iR(e.x,t.x,i.x),iR(e.y,t.y,i.y)}let iN=()=>({translate:0,scale:1,origin:0,originPoint:0}),iC=()=>({x:iN(),y:iN()}),ik=()=>({min:0,max:0}),i_=()=>({x:ik(),y:ik()});function iD(e){return[e("x"),e("y")]}function iO(e){return void 0===e||1===e}function iV({scale:e,scaleX:t,scaleY:i}){return!iO(e)||!iO(t)||!iO(i)}function iL(e){return iV(e)||iI(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function iI(e){var t,i;return(t=e.x)&&"0%"!==t||(i=e.y)&&"0%"!==i}function iF(e,t,i,r,n){return void 0!==n&&(e=r+n*(e-r)),r+i*(e-r)+t}function iU(e,t=0,i=1,r,n){e.min=iF(e.min,t,i,r,n),e.max=iF(e.max,t,i,r,n)}function i$(e,{x:t,y:i}){iU(e.x,t.translate,t.scale,t.originPoint),iU(e.y,i.translate,i.scale,i.originPoint)}function iB(e,t){e.min=e.min+t,e.max=e.max+t}function iz(e,t,i,r,n=.5){let s=eM(e.min,e.max,n);iU(e,t,i,s,r)}function iW(e,t){iz(e.x,t.x,t.scaleX,t.scale,t.originX),iz(e.y,t.y,t.scaleY,t.scale,t.originY)}function iq(e,t){return iP(function(e,t){if(!t)return e;let i=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:i.y,left:i.x,bottom:r.y,right:r.x}}(e.getBoundingClientRect(),t))}let iH=({current:e})=>e?e.ownerDocument.defaultView:null;function iX(e){return e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}let iG=(e,t)=>Math.abs(e-t);class iK{constructor(e,t,{transformPagePoint:i,contextWindow:r,dragSnapToOrigin:n=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let e=iQ(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,i=function(e,t){return Math.sqrt(iG(e.x,t.x)**2+iG(e.y,t.y)**2)}(e.offset,{x:0,y:0})>=3;if(!t&&!i)return;let{point:r}=e,{timestamp:n}=x;this.history.push({...r,timestamp:n});let{onStart:s,onMove:a}=this.handlers;t||(s&&s(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),a&&a(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=iY(t,this.transformPagePoint),v.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();let{onEnd:i,onSessionEnd:r,resumeAnimation:n}=this.handlers;if(this.dragSnapToOrigin&&n&&n(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let s=iQ("pointercancel"===e.type?this.lastMoveEventInfo:iY(t,this.transformPagePoint),this.history);this.startEvent&&i&&i(e,s),r&&r(e,s)},!ix(e))return;this.dragSnapToOrigin=n,this.handlers=t,this.transformPagePoint=i,this.contextWindow=r||window;let s=iY(ib(e),this.transformPagePoint),{point:a}=s,{timestamp:o}=x;this.history=[{...a,timestamp:o}];let{onSessionStart:l}=t;l&&l(e,iQ(s,this.history)),this.removeListeners=F(ij(this.contextWindow,"pointermove",this.handlePointerMove),ij(this.contextWindow,"pointerup",this.handlePointerUp),ij(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),y(this.updatePoint)}}function iY(e,t){return t?{point:t(e.point)}:e}function iZ(e,t){return{x:e.x-t.x,y:e.y-t.y}}function iQ({point:e},t){return{point:e,delta:iZ(e,iJ(t)),offset:iZ(e,t[0]),velocity:function(e,t){if(e.length<2)return{x:0,y:0};let i=e.length-1,r=null,n=iJ(e);for(;i>=0&&(r=e[i],!(n.timestamp-r.timestamp>$(.1)));)i--;if(!r)return{x:0,y:0};let s=B(n.timestamp-r.timestamp);if(0===s)return{x:0,y:0};let a={x:(n.x-r.x)/s,y:(n.y-r.y)/s};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}(t,.1)}}function iJ(e){return e[e.length-1]}function i0(e,t,i){return{min:void 0!==t?e.min+t:void 0,max:void 0!==i?e.max+i-(e.max-e.min):void 0}}function i1(e,t){let i=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([i,r]=[r,i]),{min:i,max:r}}function i2(e,t,i){return{min:i3(e,t),max:i3(e,i)}}function i3(e,t){return"number"==typeof e?e:e[t]||0}let i4=new WeakMap;class i5{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=i_(),this.visualElement=e}start(e,{snapToCursor:t=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:r}=this.getProps();this.panSession=new iK(e,{onSessionStart:e=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(ib(e).point)},onStart:(e,t)=>{let{drag:i,dragPropagation:r,onDragStart:n}=this.getProps();if(i&&!r&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(e){if("x"===e||"y"===e)if(iv[e])return null;else return iv[e]=!0,()=>{iv[e]=!1};return iv.x||iv.y?null:(iv.x=iv.y=!0,()=>{iv.x=iv.y=!1})}(i),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),iD(e=>{let t=this.getAxisMotionValue(e).get()||0;if(ed.test(t)){let{projection:i}=this.visualElement;if(i&&i.layout){let r=i.layout.layoutBox[e];r&&(t=iE(r)*(parseFloat(t)/100))}}this.originPoint[e]=t}),n&&v.postRender(()=>n(e,t)),O(this.visualElement,"transform");let{animationState:s}=this.visualElement;s&&s.setActive("whileDrag",!0)},onMove:(e,t)=>{let{dragPropagation:i,dragDirectionLock:r,onDirectionLock:n,onDrag:s}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:a}=t;if(r&&null===this.currentDirection){this.currentDirection=function(e,t=10){let i=null;return Math.abs(e.y)>t?i="y":Math.abs(e.x)>t&&(i="x"),i}(a),null!==this.currentDirection&&n&&n(this.currentDirection);return}this.updateAxis("x",t.point,a),this.updateAxis("y",t.point,a),this.visualElement.render(),s&&s(e,t)},onSessionEnd:(e,t)=>this.stop(e,t),resumeAnimation:()=>iD(e=>"paused"===this.getAnimationState(e)&&this.getAxisMotionValue(e).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:r,contextWindow:iH(this.visualElement)})}stop(e,t){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:r}=t;this.startAnimation(r);let{onDragEnd:n}=this.getProps();n&&v.postRender(()=>n(e,t))}cancel(){this.isDragging=!1;let{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,i){let{drag:r}=this.getProps();if(!i||!i6(e,r,this.currentDirection))return;let n=this.getAxisMotionValue(e),s=this.originPoint[e]+i[e];this.constraints&&this.constraints[e]&&(s=function(e,{min:t,max:i},r){return void 0!==t&&e<t?e=r?eM(t,e,r.min):Math.max(e,t):void 0!==i&&e>i&&(e=r?eM(i,e,r.max):Math.min(e,i)),e}(s,this.constraints[e],this.elastic[e])),n.set(s)}resolveConstraints(){let{dragConstraints:e,dragElastic:t}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,r=this.constraints;e&&iX(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&i?this.constraints=function(e,{top:t,left:i,bottom:r,right:n}){return{x:i0(e.x,i,n),y:i0(e.y,t,r)}}(i.layoutBox,e):this.constraints=!1,this.elastic=function(e=.35){return!1===e?e=0:!0===e&&(e=.35),{x:i2(e,"left","right"),y:i2(e,"top","bottom")}}(t),r!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&iD(e=>{!1!==this.constraints&&this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){let i={};return void 0!==t.min&&(i.min=t.min-e.min),void 0!==t.max&&(i.max=t.max-e.min),i}(i.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){var e;let{dragConstraints:t,onMeasureDragConstraints:i}=this.getProps();if(!t||!iX(t))return!1;let r=t.current;q(null!==r,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:n}=this.visualElement;if(!n||!n.layout)return!1;let s=function(e,t,i){let r=iq(e,i),{scroll:n}=t;return n&&(iB(r.x,n.offset.x),iB(r.y,n.offset.y)),r}(r,n.root,this.visualElement.getTransformPagePoint()),a=(e=n.layout.layoutBox,{x:i1(e.x,s.x),y:i1(e.y,s.y)});if(i){let e=i(function({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}(a));this.hasMutatedConstraints=!!e,e&&(a=iP(e))}return a}startAnimation(e){let{drag:t,dragMomentum:i,dragElastic:r,dragTransition:n,dragSnapToOrigin:s,onDragTransitionEnd:a}=this.getProps(),o=this.constraints||{};return Promise.all(iD(a=>{if(!i6(a,t,this.currentDirection))return;let l=o&&o[a]||{};s&&(l={min:0,max:0});let u={type:"inertia",velocity:i?e[a]:0,bounceStiffness:r?200:1e6,bounceDamping:r?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...n,...l};return this.startAxisValueAnimation(a,u)})).then(a)}startAxisValueAnimation(e,t){let i=this.getAxisMotionValue(e);return O(this.visualElement,e),i.start(t9(e,i,0,t,this.visualElement,!1))}stopAnimation(){iD(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){iD(e=>this.getAxisMotionValue(e).animation?.pause())}getAnimationState(e){return this.getAxisMotionValue(e).animation?.state}getAxisMotionValue(e){let t=`_drag${e.toUpperCase()}`,i=this.visualElement.getProps();return i[t]||this.visualElement.getValue(e,(i.initial?i.initial[e]:void 0)||0)}snapToCursor(e){iD(t=>{let{drag:i}=this.getProps();if(!i6(t,i,this.currentDirection))return;let{projection:r}=this.visualElement,n=this.getAxisMotionValue(t);if(r&&r.layout){let{min:i,max:s}=r.layout.layoutBox[t];n.set(e[t]-eM(i,s,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:e,dragConstraints:t}=this.getProps(),{projection:i}=this.visualElement;if(!iX(t)||!i||!this.constraints)return;this.stopAnimation();let r={x:0,y:0};iD(e=>{let t=this.getAxisMotionValue(e);if(t&&!1!==this.constraints){let i=t.get();r[e]=function(e,t){let i=.5,r=iE(e),n=iE(t);return n>r?i=tl(t.min,t.max-r,e.min):r>n&&(i=tl(e.min,e.max-n,t.min)),U(0,1,i)}({min:i,max:i},this.constraints[e])}});let{transformTemplate:n}=this.visualElement.getProps();this.visualElement.current.style.transform=n?n({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),iD(t=>{if(!i6(t,e,null))return;let i=this.getAxisMotionValue(t),{min:n,max:s}=this.constraints[t];i.set(eM(n,s,r[t]))})}addListeners(){if(!this.visualElement.current)return;i4.set(this.visualElement,this);let e=ij(this.visualElement.current,"pointerdown",e=>{let{drag:t,dragListener:i=!0}=this.getProps();t&&i&&this.start(e)}),t=()=>{let{dragConstraints:e}=this.getProps();iX(e)&&e.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,r=i.addEventListener("measure",t);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),v.read(t);let n=iy(window,"resize",()=>this.scalePositionWithinConstraints()),s=i.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t})=>{this.isDragging&&t&&(iD(t=>{let i=this.getAxisMotionValue(t);i&&(this.originPoint[t]+=e[t].translate,i.set(i.get()+e[t].translate))}),this.visualElement.render())});return()=>{n(),e(),r(),s&&s()}}getProps(){let e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:i=!1,dragPropagation:r=!1,dragConstraints:n=!1,dragElastic:s=.35,dragMomentum:a=!0}=e;return{...e,drag:t,dragDirectionLock:i,dragPropagation:r,dragConstraints:n,dragElastic:s,dragMomentum:a}}}function i6(e,t,i){return(!0===t||t===e)&&(null===i||i===e)}class i8 extends ih{constructor(e){super(e),this.removeGroupControls=h,this.removeListeners=h,this.controls=new i5(e)}mount(){let{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||h}unmount(){this.removeGroupControls(),this.removeListeners()}}let i9=e=>(t,i)=>{e&&v.postRender(()=>e(t,i))};class i7 extends ih{constructor(){super(...arguments),this.removePointerDownListener=h}onPointerDown(e){this.session=new iK(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:iH(this.node)})}createPanHandlers(){let{onPanSessionStart:e,onPanStart:t,onPan:i,onPanEnd:r}=this.node.getProps();return{onSessionStart:i9(e),onStart:i9(t),onMove:i,onEnd:(e,t)=>{delete this.session,r&&v.postRender(()=>r(e,t))}}}mount(){this.removePointerDownListener=ij(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}let{schedule:re}=g(queueMicrotask,!1);var rt=i(3210);let ri=(0,rt.createContext)(null),rr=(0,rt.createContext)({}),rn=(0,rt.createContext)({}),rs={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function ra(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}let ro={correct:(e,t)=>{if(!t.target)return e;if("string"==typeof e)if(!eh.test(e))return e;else e=parseFloat(e);let i=ra(e,t.target.x),r=ra(e,t.target.y);return`${i}% ${r}%`}},rl={};class ru extends rt.Component{componentDidMount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:i,layoutId:r}=this.props,{projection:n}=e;for(let e in rd)rl[e]=rd[e],X(e)&&(rl[e].isCSSVariable=!0);n&&(t.group&&t.group.add(n),i&&i.register&&r&&i.register(n),n.root.didUpdate(),n.addEventListener("animationComplete",()=>{this.safeToRemove()}),n.setOptions({...n.options,onExitComplete:()=>this.safeToRemove()})),rs.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){let{layoutDependency:t,visualElement:i,drag:r,isPresent:n}=this.props,{projection:s}=i;return s&&(s.isPresent=n,r||e.layoutDependency!==t||void 0===t||e.isPresent!==n?s.willUpdate():this.safeToRemove(),e.isPresent!==n&&(n?s.promote():s.relegate()||v.postRender(()=>{let e=s.getStack();e&&e.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),re.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:i}=this.props,{projection:r}=e;r&&(r.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(r),i&&i.deregister&&i.deregister(r))}safeToRemove(){let{safeToRemove:e}=this.props;e&&e()}render(){return null}}function rc(e){let[t,i]=function(e=!0){let t=(0,rt.useContext)(ri);if(null===t)return[!0,null];let{isPresent:i,onExitComplete:r,register:n}=t,s=(0,rt.useId)();(0,rt.useEffect)(()=>{if(e)return n(s)},[e]);let a=(0,rt.useCallback)(()=>e&&r&&r(s),[s,r,e]);return!i&&r?[!1,a]:[!0]}(),r=(0,rt.useContext)(rr);return(0,a.jsx)(ru,{...e,layoutGroup:r,switchLayoutGroup:(0,rt.useContext)(rn),isPresent:t,safeToRemove:i})}let rd={borderRadius:{...ro,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:ro,borderTopRightRadius:ro,borderBottomLeftRadius:ro,borderBottomRightRadius:ro,boxShadow:{correct:(e,{treeScale:t,projectionDelta:i})=>{let r=eS.parse(e);if(r.length>5)return e;let n=eS.createTransformer(e),s=+("number"!=typeof r[0]),a=i.x.scale*t.x,o=i.y.scale*t.y;r[0+s]/=a,r[1+s]/=o;let l=eM(a,o,.5);return"number"==typeof r[2+s]&&(r[2+s]/=l),"number"==typeof r[3+s]&&(r[3+s]/=l),n(r)}}};function rh(e){return tZ(e)&&"ownerSVGElement"in e}let rp=(e,t)=>e.depth-t.depth;class rm{constructor(){this.children=[],this.isDirty=!1}add(e){E(this.children,e),this.isDirty=!0}remove(e){T(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(rp),this.isDirty=!1,this.children.forEach(e)}}function rf(e){return D(e)?e.get():e}let rg=["TopLeft","TopRight","BottomLeft","BottomRight"],rv=rg.length,ry=e=>"string"==typeof e?parseFloat(e):e,rx=e=>"number"==typeof e||eh.test(e);function rb(e,t){return void 0!==e[t]?e[t]:e.borderRadius}let rw=rP(0,.5,ti),rj=rP(.5,.95,h);function rP(e,t,i){return r=>r<e?0:r>t?1:i(tl(e,t,r))}function rE(e,t){e.min=t.min,e.max=t.max}function rT(e,t){rE(e.x,t.x),rE(e.y,t.y)}function rS(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function rA(e,t,i,r,n){return e-=t,e=r+1/i*(e-r),void 0!==n&&(e=r+1/n*(e-r)),e}function rR(e,t,[i,r,n],s,a){!function(e,t=0,i=1,r=.5,n,s=e,a=e){if(ed.test(t)&&(t=parseFloat(t),t=eM(a.min,a.max,t/100)-a.min),"number"!=typeof t)return;let o=eM(s.min,s.max,r);e===s&&(o-=t),e.min=rA(e.min,t,i,o,n),e.max=rA(e.max,t,i,o,n)}(e,t[i],t[r],t[n],t.scale,s,a)}let rM=["x","scaleX","originX"],rN=["y","scaleY","originY"];function rC(e,t,i,r){rR(e.x,t,rM,i?i.x:void 0,r?r.x:void 0),rR(e.y,t,rN,i?i.y:void 0,r?r.y:void 0)}function rk(e){return 0===e.translate&&1===e.scale}function r_(e){return rk(e.x)&&rk(e.y)}function rD(e,t){return e.min===t.min&&e.max===t.max}function rO(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function rV(e,t){return rO(e.x,t.x)&&rO(e.y,t.y)}function rL(e){return iE(e.x)/iE(e.y)}function rI(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class rF{constructor(){this.members=[]}add(e){E(this.members,e),e.scheduleRender()}remove(e){if(T(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){let e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){let t,i=this.members.findIndex(t=>e===t);if(0===i)return!1;for(let e=i;e>=0;e--){let i=this.members[e];if(!1!==i.isPresent){t=i;break}}return!!t&&(this.promote(t),!0)}promote(e,t){let i=this.lead;if(e!==i&&(this.prevLead=i,this.lead=e,e.show(),i)){i.instance&&i.scheduleRender(),e.scheduleRender(),e.resumeFrom=i,t&&(e.resumeFrom.preserveOpacity=!0),i.snapshot&&(e.snapshot=i.snapshot,e.snapshot.latestValues=i.animationValues||i.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);let{crossfade:r}=e.options;!1===r&&i.hide()}}exitAnimationComplete(){this.members.forEach(e=>{let{options:t,resumingFrom:i}=e;t.onExitComplete&&t.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let rU={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},r$=["","X","Y","Z"],rB={visibility:"hidden"},rz=0;function rW(e,t,i,r){let{latestValues:n}=t;n[e]&&(i[e]=n[e],t.setStaticValue(e,0),r&&(r[e]=0))}function rq({attachResizeListener:e,defaultParent:t,measureScroll:i,checkIsScrollRoot:r,resetTransform:n}){return class{constructor(e={},i=t?.()){this.id=rz++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,f.value&&(rU.nodes=rU.calculatedTargetDeltas=rU.calculatedProjections=0),this.nodes.forEach(rG),this.nodes.forEach(r1),this.nodes.forEach(r2),this.nodes.forEach(rK),f.addProjectionMetrics&&f.addProjectionMetrics(rU)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new rm)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new S),this.eventHandlers.get(e).add(t)}notifyListeners(e,...t){let i=this.eventHandlers.get(e);i&&i.notify(...t)}hasListeners(e){return this.eventHandlers.has(e)}mount(t){if(this.instance)return;this.isSVG=rh(t)&&!(rh(t)&&"svg"===t.tagName),this.instance=t;let{layoutId:i,layout:r,visualElement:n}=this.options;if(n&&!n.current&&n.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(r||i)&&(this.isLayoutDirty=!0),e){let i,r=()=>this.root.updateBlockedByResize=!1;e(t,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(e,t){let i=R.now(),r=({timestamp:n})=>{let s=n-i;s>=250&&(y(r),e(s-t))};return v.setup(r,!0),()=>y(r)}(r,250),rs.hasAnimatedSinceResize&&(rs.hasAnimatedSinceResize=!1,this.nodes.forEach(r0))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&n&&(i||r)&&this.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t,hasRelativeLayoutChanged:i,layout:r})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let s=this.options.transition||n.getDefaultTransition()||r9,{onLayoutAnimationStart:a,onLayoutAnimationComplete:o}=n.getProps(),l=!this.targetLayout||!rV(this.targetLayout,r),u=!t&&i;if(this.options.layoutRoot||this.resumeFrom||u||t&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let t={...d(s,"layout"),onPlay:a,onComplete:o};(n.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t),this.setAnimationOrigin(e,u)}else t||r0(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=r})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),y(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(r3),this.animationId++)}getTransformTemplate(){let{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(e=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function e(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;let{visualElement:i}=t.options;if(!i)return;let r=i.props[L];if(window.MotionHasOptimisedAnimation(r,"transform")){let{layout:e,layoutId:i}=t.options;window.MotionCancelOptimisedAnimation(r,"transform",v,!(e||i))}let{parent:n}=t;n&&!n.hasCheckedOptimisedAppear&&e(n)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let e=0;e<this.path.length;e++){let t=this.path[e];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}let{layoutId:t,layout:i}=this.options;if(void 0===t&&!i)return;let r=this.getTransformTemplate();this.prevTransformTemplateValue=r?r(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(rZ);return}this.isUpdating||this.nodes.forEach(rQ),this.isUpdating=!1,this.nodes.forEach(rJ),this.nodes.forEach(rH),this.nodes.forEach(rX),this.clearAllSnapshots();let e=R.now();x.delta=U(0,1e3/60,e-x.timestamp),x.timestamp=e,x.isProcessing=!0,b.update.process(x),b.preRender.process(x),b.render.process(x),x.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,re.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(rY),this.sharedNodes.forEach(r4)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,v.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){v.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||iE(this.snapshot.measuredBox.x)||iE(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let e=0;e<this.path.length;e++)this.path[e].updateScroll();let e=this.layout;this.layout=this.measure(!1),this.layoutCorrected=i_(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(e="measure"){let t=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t&&this.instance){let t=r(this.instance);this.scroll={animationId:this.root.animationId,phase:e,isRoot:t,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:t}}}resetTransform(){if(!n)return;let e=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,t=this.projectionDelta&&!r_(this.projectionDelta),i=this.getTransformTemplate(),r=i?i(this.latestValues,""):void 0,s=r!==this.prevTransformTemplateValue;e&&this.instance&&(t||iL(this.latestValues)||s)&&(n(this.instance,r),this.shouldResetTransform=!1,this.scheduleRender())}measure(e=!0){var t;let i=this.measurePageBox(),r=this.removeElementScroll(i);return e&&(r=this.removeTransform(r)),nt((t=r).x),nt(t.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:r,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:e}=this.options;if(!e)return i_();let t=e.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(nr))){let{scroll:e}=this.root;e&&(iB(t.x,e.offset.x),iB(t.y,e.offset.y))}return t}removeElementScroll(e){let t=i_();if(rT(t,e),this.scroll?.wasRoot)return t;for(let i=0;i<this.path.length;i++){let r=this.path[i],{scroll:n,options:s}=r;r!==this.root&&n&&s.layoutScroll&&(n.wasRoot&&rT(t,e),iB(t.x,n.offset.x),iB(t.y,n.offset.y))}return t}applyTransform(e,t=!1){let i=i_();rT(i,e);for(let e=0;e<this.path.length;e++){let r=this.path[e];!t&&r.options.layoutScroll&&r.scroll&&r!==r.root&&iW(i,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),iL(r.latestValues)&&iW(i,r.latestValues)}return iL(this.latestValues)&&iW(i,this.latestValues),i}removeTransform(e){let t=i_();rT(t,e);for(let e=0;e<this.path.length;e++){let i=this.path[e];if(!i.instance||!iL(i.latestValues))continue;iV(i.latestValues)&&i.updateSnapshot();let r=i_();rT(r,i.measurePageBox()),rC(t,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,r)}return iL(this.latestValues)&&rC(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options={...this.options,...e,crossfade:void 0===e.crossfade||e.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==x.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(e=!1){let t=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=t.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=t.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=t.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==t;if(!(e||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:r,layoutId:n}=this.options;if(this.layout&&(r||n)){if(this.resolvedRelativeTargetAt=x.timestamp,!this.targetDelta&&!this.relativeTarget){let e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=i_(),this.relativeTargetOrigin=i_(),iM(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),rT(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=i_(),this.targetWithTransforms=i_()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var s,a,o;this.forceRelativeParentToResolveTarget(),s=this.target,a=this.relativeTarget,o=this.relativeParent.target,iA(s.x,a.x,o.x),iA(s.y,a.y,o.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):rT(this.target,this.layout.layoutBox),i$(this.target,this.targetDelta)):rT(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let e=this.getClosestProjectingParent();e&&!!e.resumingFrom==!!this.resumingFrom&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=i_(),this.relativeTargetOrigin=i_(),iM(this.relativeTargetOrigin,this.target,e.target),rT(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}f.value&&rU.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||iV(this.parent.latestValues)||iI(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let e=this.getLead(),t=!!this.resumingFrom||this!==e,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),t&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===x.timestamp&&(i=!1),i)return;let{layout:r,layoutId:n}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(r||n))return;rT(this.layoutCorrected,this.layout.layoutBox);let s=this.treeScale.x,a=this.treeScale.y;!function(e,t,i,r=!1){let n,s,a=i.length;if(a){t.x=t.y=1;for(let o=0;o<a;o++){s=(n=i[o]).projectionDelta;let{visualElement:a}=n.options;(!a||!a.props.style||"contents"!==a.props.style.display)&&(r&&n.options.layoutScroll&&n.scroll&&n!==n.root&&iW(e,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),s&&(t.x*=s.x.scale,t.y*=s.y.scale,i$(e,s)),r&&iL(n.latestValues)&&iW(e,n.latestValues))}t.x<1.0000000000001&&t.x>.999999999999&&(t.x=1),t.y<1.0000000000001&&t.y>.999999999999&&(t.y=1)}}(this.layoutCorrected,this.treeScale,this.path,t),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox,e.targetWithTransforms=i_());let{target:o}=e;if(!o){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(rS(this.prevProjectionDelta.x,this.projectionDelta.x),rS(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),iS(this.projectionDelta,this.layoutCorrected,o,this.latestValues),this.treeScale.x===s&&this.treeScale.y===a&&rI(this.projectionDelta.x,this.prevProjectionDelta.x)&&rI(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",o)),f.value&&rU.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(e=!0){if(this.options.visualElement?.scheduleRender(),e){let e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=iC(),this.projectionDelta=iC(),this.projectionDeltaWithTransform=iC()}setAnimationOrigin(e,t=!1){let i,r=this.snapshot,n=r?r.latestValues:{},s={...this.latestValues},a=iC();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;let o=i_(),l=(r?r.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),c=!u||u.members.length<=1,d=!!(l&&!c&&!0===this.options.crossfade&&!this.path.some(r8));this.animationProgress=0,this.mixTargetDelta=t=>{let r=t/1e3;if(r5(a.x,e.x,r),r5(a.y,e.y,r),this.setTargetDelta(a),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,h,p,m,f,g;iM(o,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=o,g=r,r6(p.x,m.x,f.x,g),r6(p.y,m.y,f.y,g),i&&(u=this.relativeTarget,h=i,rD(u.x,h.x)&&rD(u.y,h.y))&&(this.isProjectionDirty=!1),i||(i=i_()),rT(i,this.relativeTarget)}l&&(this.animationValues=s,function(e,t,i,r,n,s){n?(e.opacity=eM(0,i.opacity??1,rw(r)),e.opacityExit=eM(t.opacity??1,0,rj(r))):s&&(e.opacity=eM(t.opacity??1,i.opacity??1,r));for(let n=0;n<rv;n++){let s=`border${rg[n]}Radius`,a=rb(t,s),o=rb(i,s);(void 0!==a||void 0!==o)&&(a||(a=0),o||(o=0),0===a||0===o||rx(a)===rx(o)?(e[s]=Math.max(eM(ry(a),ry(o),r),0),(ed.test(o)||ed.test(a))&&(e[s]+="%")):e[s]=o)}(t.rotate||i.rotate)&&(e.rotate=eM(t.rotate||0,i.rotate||0,r))}(s,n,this.latestValues,r,d,c)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=r},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(e){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(y(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=v.update(()=>{rs.hasAnimatedSinceResize=!0,z.layout++,this.motionValue||(this.motionValue=k(0)),this.currentAnimation=function(e,t,i){let r=D(e)?e:k(e);return r.start(t9("",r,t,i)),r.animation}(this.motionValue,[0,1e3],{...e,velocity:0,isSync:!0,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onStop:()=>{z.layout--},onComplete:()=>{z.layout--,e.onComplete&&e.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let e=this.getLead(),{targetWithTransforms:t,target:i,layout:r,latestValues:n}=e;if(t&&i&&r){if(this!==e&&this.layout&&r&&ni(this.options.animationType,this.layout.layoutBox,r.layoutBox)){i=this.target||i_();let t=iE(this.layout.layoutBox.x);i.x.min=e.target.x.min,i.x.max=i.x.min+t;let r=iE(this.layout.layoutBox.y);i.y.min=e.target.y.min,i.y.max=i.y.min+r}rT(t,i),iW(t,n),iS(this.projectionDeltaWithTransform,this.layoutCorrected,t,n)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new rF),this.sharedNodes.get(e).add(t);let i=t.options.initialPromotionConfig;t.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(t):void 0})}isLead(){let e=this.getStack();return!e||e.lead===this}getLead(){let{layoutId:e}=this.options;return e&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:e}=this.options;return e?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote({needsReset:e,transition:t,preserveFollowOpacity:i}={}){let r=this.getStack();r&&r.promote(this,i),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){let e=this.getStack();return!!e&&e.relegate(this)}resetSkewAndRotation(){let{visualElement:e}=this.options;if(!e)return;let t=!1,{latestValues:i}=e;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(t=!0),!t)return;let r={};i.z&&rW("z",e,r,this.animationValues);for(let t=0;t<r$.length;t++)rW(`rotate${r$[t]}`,e,r,this.animationValues),rW(`skew${r$[t]}`,e,r,this.animationValues);for(let t in e.render(),r)e.setStaticValue(t,r[t]),this.animationValues&&(this.animationValues[t]=r[t]);e.scheduleRender()}getProjectionStyles(e){if(!this.instance||this.isSVG)return;if(!this.isVisible)return rB;let t={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,t.opacity="",t.pointerEvents=rf(e?.pointerEvents)||"",t.transform=i?i(this.latestValues,""):"none",t;let r=this.getLead();if(!this.projectionDelta||!this.layout||!r.target){let t={};return this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=rf(e?.pointerEvents)||""),this.hasProjected&&!iL(this.latestValues)&&(t.transform=i?i({},""):"none",this.hasProjected=!1),t}let n=r.animationValues||r.latestValues;this.applyTransformsToTarget(),t.transform=function(e,t,i){let r="",n=e.x.translate/t.x,s=e.y.translate/t.y,a=i?.z||0;if((n||s||a)&&(r=`translate3d(${n}px, ${s}px, ${a}px) `),(1!==t.x||1!==t.y)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),i){let{transformPerspective:e,rotate:t,rotateX:n,rotateY:s,skewX:a,skewY:o}=i;e&&(r=`perspective(${e}px) ${r}`),t&&(r+=`rotate(${t}deg) `),n&&(r+=`rotateX(${n}deg) `),s&&(r+=`rotateY(${s}deg) `),a&&(r+=`skewX(${a}deg) `),o&&(r+=`skewY(${o}deg) `)}let o=e.x.scale*t.x,l=e.y.scale*t.y;return(1!==o||1!==l)&&(r+=`scale(${o}, ${l})`),r||"none"}(this.projectionDeltaWithTransform,this.treeScale,n),i&&(t.transform=i(n,t.transform));let{x:s,y:a}=this.projectionDelta;for(let e in t.transformOrigin=`${100*s.origin}% ${100*a.origin}% 0`,r.animationValues?t.opacity=r===this?n.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:n.opacityExit:t.opacity=r===this?void 0!==n.opacity?n.opacity:"":void 0!==n.opacityExit?n.opacityExit:0,rl){if(void 0===n[e])continue;let{correct:i,applyTo:s,isCSSVariable:a}=rl[e],o="none"===t.transform?n[e]:i(n[e],r);if(s){let e=s.length;for(let i=0;i<e;i++)t[s[i]]=o}else a?this.options.visualElement.renderState.vars[e]=o:t[e]=o}return this.options.layoutId&&(t.pointerEvents=r===this?rf(e?.pointerEvents)||"":"none"),t}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>e.currentAnimation?.stop()),this.root.nodes.forEach(rZ),this.root.sharedNodes.clear()}}}function rH(e){e.updateLayout()}function rX(e){let t=e.resumeFrom?.snapshot||e.snapshot;if(e.isLead()&&e.layout&&t&&e.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:r}=e.layout,{animationType:n}=e.options,s=t.source!==e.layout.source;"size"===n?iD(e=>{let r=s?t.measuredBox[e]:t.layoutBox[e],n=iE(r);r.min=i[e].min,r.max=r.min+n}):ni(n,t.layoutBox,i)&&iD(r=>{let n=s?t.measuredBox[r]:t.layoutBox[r],a=iE(i[r]);n.max=n.min+a,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[r].max=e.relativeTarget[r].min+a)});let a=iC();iS(a,i,t.layoutBox);let o=iC();s?iS(o,e.applyTransform(r,!0),t.measuredBox):iS(o,i,t.layoutBox);let l=!r_(a),u=!1;if(!e.resumeFrom){let r=e.getClosestProjectingParent();if(r&&!r.resumeFrom){let{snapshot:n,layout:s}=r;if(n&&s){let a=i_();iM(a,t.layoutBox,n.layoutBox);let o=i_();iM(o,i,s.layoutBox),rV(a,o)||(u=!0),r.options.layoutRoot&&(e.relativeTarget=o,e.relativeTargetOrigin=a,e.relativeParent=r)}}}e.notifyListeners("didUpdate",{layout:i,snapshot:t,delta:o,layoutDelta:a,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(e.isLead()){let{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function rG(e){f.value&&rU.nodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function rK(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function rY(e){e.clearSnapshot()}function rZ(e){e.clearMeasurements()}function rQ(e){e.isLayoutDirty=!1}function rJ(e){let{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function r0(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function r1(e){e.resolveTargetDelta()}function r2(e){e.calcProjection()}function r3(e){e.resetSkewAndRotation()}function r4(e){e.removeLeadSnapshot()}function r5(e,t,i){e.translate=eM(t.translate,0,i),e.scale=eM(t.scale,1,i),e.origin=t.origin,e.originPoint=t.originPoint}function r6(e,t,i,r){e.min=eM(t.min,i.min,r),e.max=eM(t.max,i.max,r)}function r8(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}let r9={duration:.45,ease:[.4,0,.1,1]},r7=e=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),ne=r7("applewebkit/")&&!r7("chrome/")?Math.round:h;function nt(e){e.min=ne(e.min),e.max=ne(e.max)}function ni(e,t,i){return"position"===e||"preserve-aspect"===e&&!(.2>=Math.abs(rL(t)-rL(i)))}function nr(e){return e!==e.root&&e.scroll?.wasRoot}let nn=rq({attachResizeListener:(e,t)=>iy(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),ns={current:void 0},na=rq({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!ns.current){let e=new nn({});e.mount(window),e.setOptions({layoutScroll:!0}),ns.current=e}return ns.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>"fixed"===window.getComputedStyle(e).position});function no(e,t){let i=function(e,t,i){if(e instanceof EventTarget)return[e];if("string"==typeof e){let t=document,i=(void 0)??t.querySelectorAll(e);return i?Array.from(i):[]}return Array.from(e)}(e),r=new AbortController;return[i,{passive:!0,...t,signal:r.signal},()=>r.abort()]}function nl(e){return!("touch"===e.pointerType||iv.x||iv.y)}function nu(e,t,i){let{props:r}=e;e.animationState&&r.whileHover&&e.animationState.setActive("whileHover","Start"===i);let n=r["onHover"+i];n&&v.postRender(()=>n(t,ib(t)))}class nc extends ih{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,i={}){let[r,n,s]=no(e,i),a=e=>{if(!nl(e))return;let{target:i}=e,r=t(i,e);if("function"!=typeof r||!i)return;let s=e=>{nl(e)&&(r(e),i.removeEventListener("pointerleave",s))};i.addEventListener("pointerleave",s,n)};return r.forEach(e=>{e.addEventListener("pointerenter",a,n)}),s}(e,(e,t)=>(nu(this.node,t,"Start"),e=>nu(this.node,e,"End"))))}unmount(){}}class nd extends ih{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=F(iy(this.node.current,"focus",()=>this.onFocus()),iy(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let nh=(e,t)=>!!t&&(e===t||nh(e,t.parentElement)),np=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),nm=new WeakSet;function nf(e){return t=>{"Enter"===t.key&&e(t)}}function ng(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}let nv=(e,t)=>{let i=e.currentTarget;if(!i)return;let r=nf(()=>{if(nm.has(i))return;ng(i,"down");let e=nf(()=>{ng(i,"up")});i.addEventListener("keyup",e,t),i.addEventListener("blur",()=>ng(i,"cancel"),t)});i.addEventListener("keydown",r,t),i.addEventListener("blur",()=>i.removeEventListener("keydown",r),t)};function ny(e){return ix(e)&&!(iv.x||iv.y)}function nx(e,t,i){let{props:r}=e;if(e.current instanceof HTMLButtonElement&&e.current.disabled)return;e.animationState&&r.whileTap&&e.animationState.setActive("whileTap","Start"===i);let n=r["onTap"+("End"===i?"":i)];n&&v.postRender(()=>n(t,ib(t)))}class nb extends ih{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,i={}){let[r,n,s]=no(e,i),a=e=>{let r=e.currentTarget;if(!ny(e))return;nm.add(r);let s=t(r,e),a=(e,t)=>{window.removeEventListener("pointerup",o),window.removeEventListener("pointercancel",l),nm.has(r)&&nm.delete(r),ny(e)&&"function"==typeof s&&s(e,{success:t})},o=e=>{a(e,r===window||r===document||i.useGlobalTarget||nh(r,e.target))},l=e=>{a(e,!1)};window.addEventListener("pointerup",o,n),window.addEventListener("pointercancel",l,n)};return r.forEach(e=>{((i.useGlobalTarget?window:e).addEventListener("pointerdown",a,n),tQ(e))&&(e.addEventListener("focus",e=>nv(e,n)),np.has(e.tagName)||-1!==e.tabIndex||e.hasAttribute("tabindex")||(e.tabIndex=0))}),s}(e,(e,t)=>(nx(this.node,t,"Start"),(e,{success:t})=>nx(this.node,e,t?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let nw=new WeakMap,nj=new WeakMap,nP=e=>{let t=nw.get(e.target);t&&t(e)},nE=e=>{e.forEach(nP)},nT={some:0,all:1};class nS extends ih{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:e={}}=this.node.getProps(),{root:t,margin:i,amount:r="some",once:n}=e,s={root:t?t.current:void 0,rootMargin:i,threshold:"number"==typeof r?r:nT[r]};return function(e,t,i){let r=function({root:e,...t}){let i=e||document;nj.has(i)||nj.set(i,{});let r=nj.get(i),n=JSON.stringify(t);return r[n]||(r[n]=new IntersectionObserver(nE,{root:e,...t})),r[n]}(t);return nw.set(e,i),r.observe(e),()=>{nw.delete(e),r.unobserve(e)}}(this.node.current,s,e=>{let{isIntersecting:t}=e;if(this.isInView===t||(this.isInView=t,n&&!t&&this.hasEnteredView))return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);let{onViewportEnter:i,onViewportLeave:r}=this.node.getProps(),s=t?i:r;s&&s(e)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:e,prevProps:t}=this.node;["amount","margin","root"].some(function({viewport:e={}},{viewport:t={}}={}){return i=>e[i]!==t[i]}(e,t))&&this.startObserver()}unmount(){}}let nA=(0,rt.createContext)({strict:!1}),nR=(0,rt.createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),nM=(0,rt.createContext)({});function nN(e){return o(e.animate)||ia.some(t=>ir(e[t]))}function nC(e){return!!(nN(e)||e.variants)}function nk(e){return Array.isArray(e)?e.join(" "):e}let n_="undefined"!=typeof window,nD={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},nO={};for(let e in nD)nO[e]={isEnabled:t=>nD[e].some(e=>!!t[e])};let nV=Symbol.for("motionComponentSymbol"),nL=n_?rt.useLayoutEffect:rt.useEffect;function nI(e,{layout:t,layoutId:i}){return j.has(e)||e.startsWith("origin")||(t||void 0!==i)&&(!!rl[e]||"opacity"===e)}let nF=(e,t)=>t&&"number"==typeof e?t.transform(e):e,nU={...Z,transform:Math.round},n$={borderWidth:eh,borderTopWidth:eh,borderRightWidth:eh,borderBottomWidth:eh,borderLeftWidth:eh,borderRadius:eh,radius:eh,borderTopLeftRadius:eh,borderTopRightRadius:eh,borderBottomRightRadius:eh,borderBottomLeftRadius:eh,width:eh,maxWidth:eh,height:eh,maxHeight:eh,top:eh,right:eh,bottom:eh,left:eh,padding:eh,paddingTop:eh,paddingRight:eh,paddingBottom:eh,paddingLeft:eh,margin:eh,marginTop:eh,marginRight:eh,marginBottom:eh,marginLeft:eh,backgroundPositionX:eh,backgroundPositionY:eh,rotate:ec,rotateX:ec,rotateY:ec,rotateZ:ec,scale:J,scaleX:J,scaleY:J,scaleZ:J,skew:ec,skewX:ec,skewY:ec,distance:eh,translateX:eh,translateY:eh,translateZ:eh,x:eh,y:eh,z:eh,perspective:eh,transformPerspective:eh,opacity:Q,originX:ef,originY:ef,originZ:eh,zIndex:nU,fillOpacity:Q,strokeOpacity:Q,numOctaves:nU},nB={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},nz=w.length;function nW(e,t,i){let{style:r,vars:n,transformOrigin:s}=e,a=!1,o=!1;for(let e in t){let i=t[e];if(j.has(e)){a=!0;continue}if(X(e)){n[e]=i;continue}{let t=nF(i,n$[e]);e.startsWith("origin")?(o=!0,s[e]=t):r[e]=t}}if(!t.transform&&(a||i?r.transform=function(e,t,i){let r="",n=!0;for(let s=0;s<nz;s++){let a=w[s],o=e[a];if(void 0===o)continue;let l=!0;if(!(l="number"==typeof o?o===+!!a.startsWith("scale"):0===parseFloat(o))||i){let e=nF(o,n$[a]);if(!l){n=!1;let t=nB[a]||a;r+=`${t}(${e}) `}i&&(t[a]=e)}}return r=r.trim(),i?r=i(t,n?"":r):n&&(r="none"),r}(t,e.transform,i):r.transform&&(r.transform="none")),o){let{originX:e="50%",originY:t="50%",originZ:i=0}=s;r.transformOrigin=`${e} ${t} ${i}`}}let nq=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function nH(e,t,i){for(let r in t)D(t[r])||nI(r,i)||(e[r]=t[r])}let nX={offset:"stroke-dashoffset",array:"stroke-dasharray"},nG={offset:"strokeDashoffset",array:"strokeDasharray"};function nK(e,{attrX:t,attrY:i,attrScale:r,pathLength:n,pathSpacing:s=1,pathOffset:a=0,...o},l,u,c){if(nW(e,o,u),l){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:d,style:h}=e;d.transform&&(h.transform=d.transform,delete d.transform),(h.transform||d.transformOrigin)&&(h.transformOrigin=d.transformOrigin??"50% 50%",delete d.transformOrigin),h.transform&&(h.transformBox=c?.transformBox??"fill-box",delete d.transformBox),void 0!==t&&(d.x=t),void 0!==i&&(d.y=i),void 0!==r&&(d.scale=r),void 0!==n&&function(e,t,i=1,r=0,n=!0){e.pathLength=1;let s=n?nX:nG;e[s.offset]=eh.transform(-r);let a=eh.transform(t),o=eh.transform(i);e[s.array]=`${a} ${o}`}(d,n,s,a,!1)}let nY=()=>({...nq(),attrs:{}}),nZ=e=>"string"==typeof e&&"svg"===e.toLowerCase(),nQ=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function nJ(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||nQ.has(e)}let n0=e=>!nJ(e);try{!function(e){"function"==typeof e&&(n0=t=>t.startsWith("on")?!nJ(t):e(t))}(require("@emotion/is-prop-valid").default)}catch{}let n1=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function n2(e){if("string"!=typeof e||e.includes("-"));else if(n1.indexOf(e)>-1||/[A-Z]/u.test(e))return!0;return!1}let n3=e=>(t,i)=>{let r=(0,rt.useContext)(nM),n=(0,rt.useContext)(ri),s=()=>(function({scrapeMotionValuesFromProps:e,createRenderState:t},i,r,n){return{latestValues:function(e,t,i,r){let n={},s=r(e,{});for(let e in s)n[e]=rf(s[e]);let{initial:a,animate:l}=e,c=nN(e),d=nC(e);t&&d&&!c&&!1!==e.inherit&&(void 0===a&&(a=t.initial),void 0===l&&(l=t.animate));let h=!!i&&!1===i.initial,p=(h=h||!1===a)?l:a;if(p&&"boolean"!=typeof p&&!o(p)){let t=Array.isArray(p)?p:[p];for(let i=0;i<t.length;i++){let r=u(e,t[i]);if(r){let{transitionEnd:e,transition:t,...i}=r;for(let e in i){let t=i[e];if(Array.isArray(t)){let e=h?t.length-1:0;t=t[e]}null!==t&&(n[e]=t)}for(let t in e)n[t]=e[t]}}}return n}(i,r,n,e),renderState:t()}})(e,t,r,n);return i?s():function(e){let t=(0,rt.useRef)(null);return null===t.current&&(t.current=e()),t.current}(s)};function n4(e,t,i){let{style:r}=e,n={};for(let s in r)(D(r[s])||t.style&&D(t.style[s])||nI(s,e)||i?.getValue(s)?.liveStyle!==void 0)&&(n[s]=r[s]);return n}let n5={useVisualState:n3({scrapeMotionValuesFromProps:n4,createRenderState:nq})};function n6(e,t,i){let r=n4(e,t,i);for(let i in e)(D(e[i])||D(t[i]))&&(r[-1!==w.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=e[i]);return r}let n8={useVisualState:n3({scrapeMotionValuesFromProps:n6,createRenderState:nY})},n9=e=>t=>t.test(e),n7=[Z,eh,ed,ec,em,ep,{test:e=>"auto"===e,parse:e=>e}],se=e=>n7.find(n9(e)),st=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),si=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,sr=e=>/^0[^.\s]+$/u.test(e),sn=new Set(["brightness","contrast","saturate","opacity"]);function ss(e){let[t,i]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;let[r]=i.match(et)||[];if(!r)return e;let n=i.replace(r,""),s=+!!sn.has(t);return r!==i&&(s*=100),t+"("+s+n+")"}let sa=/\b([a-z-]*)\(.*?\)/gu,so={...eS,getAnimatableNone:e=>{let t=e.match(sa);return t?t.map(ss).join(" "):e}},sl={...n$,color:ev,backgroundColor:ev,outlineColor:ev,fill:ev,stroke:ev,borderColor:ev,borderTopColor:ev,borderRightColor:ev,borderBottomColor:ev,borderLeftColor:ev,filter:so,WebkitFilter:so},su=e=>sl[e];function sc(e,t){let i=su(e);return i!==so&&(i=eS),i.getAnimatableNone?i.getAnimatableNone(t):void 0}let sd=new Set(["auto","none","0"]);class sh extends tI{constructor(e,t,i,r,n){super(e,t,i,r,n,!0)}readKeyframes(){let{unresolvedKeyframes:e,element:t,name:i}=this;if(!t||!t.current)return;super.readKeyframes();for(let i=0;i<e.length;i++){let r=e[i];if("string"==typeof r&&K(r=r.trim())){let n=function e(t,i,r=1){q(r<=4,`Max CSS variable fallback depth detected in property "${t}". This may indicate a circular fallback dependency.`);let[n,s]=function(e){let t=si.exec(e);if(!t)return[,];let[,i,r,n]=t;return[`--${i??r}`,n]}(t);if(!n)return;let a=window.getComputedStyle(i).getPropertyValue(n);if(a){let e=a.trim();return st(e)?parseFloat(e):e}return K(s)?e(s,i,r+1):s}(r,t.current);void 0!==n&&(e[i]=n),i===e.length-1&&(this.finalKeyframe=r)}}if(this.resolveNoneKeyframes(),!P.has(i)||2!==e.length)return;let[r,n]=e,s=se(r),a=se(n);if(s!==a)if(tR(s)&&tR(a))for(let t=0;t<e.length;t++){let i=e[t];"string"==typeof i&&(e[t]=parseFloat(i))}else tC[i]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:e,name:t}=this,i=[];for(let t=0;t<e.length;t++){var r;(null===e[t]||("number"==typeof(r=e[t])?0===r:null===r||"none"===r||"0"===r||sr(r)))&&i.push(t)}i.length&&function(e,t,i){let r,n=0;for(;n<e.length&&!r;){let t=e[n];"string"==typeof t&&!sd.has(t)&&ej(t).values.length&&(r=e[n]),n++}if(r&&i)for(let n of t)e[n]=sc(i,r)}(e,i,t)}measureInitialState(){let{element:e,unresolvedKeyframes:t,name:i}=this;if(!e||!e.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=tC[i](e.measureViewportBox(),window.getComputedStyle(e.current)),t[0]=this.measuredOrigin;let r=t[t.length-1];void 0!==r&&e.getValue(i,r).jump(r,!1)}measureEndState(){let{element:e,name:t,unresolvedKeyframes:i}=this;if(!e||!e.current)return;let r=e.getValue(t);r&&r.jump(this.measuredOrigin,!1);let n=i.length-1,s=i[n];i[n]=tC[t](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==s&&void 0===this.finalKeyframe&&(this.finalKeyframe=s),this.removedTransforms?.length&&this.removedTransforms.forEach(([t,i])=>{e.getValue(t).set(i)}),this.resolveNoneKeyframes()}}let sp=[...n7,ev,eS],sm=e=>sp.find(n9(e)),sf={current:null},sg={current:!1},sv=new WeakMap,sy=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class sx{scrapeMotionValuesFromProps(e,t,i){return{}}constructor({parent:e,props:t,presenceContext:i,reducedMotionConfig:r,blockInitialAnimation:n,visualState:s},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=tI,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let e=R.now();this.renderScheduledAt<e&&(this.renderScheduledAt=e,v.render(this.render,!1,!0))};let{latestValues:o,renderState:l}=s;this.latestValues=o,this.baseTarget={...o},this.initialValues=t.initial?{...o}:{},this.renderState=l,this.parent=e,this.props=t,this.presenceContext=i,this.depth=e?e.depth+1:0,this.reducedMotionConfig=r,this.options=a,this.blockInitialAnimation=!!n,this.isControllingVariants=nN(t),this.isVariantNode=nC(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);let{willChange:u,...c}=this.scrapeMotionValuesFromProps(t,{},this);for(let e in c){let t=c[e];void 0!==o[e]&&D(t)&&t.set(o[e],!1)}}mount(e){this.current=e,sv.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),sg.current||function(){if(sg.current=!0,n_)if(window.matchMedia){let e=window.matchMedia("(prefers-reduced-motion)"),t=()=>sf.current=e.matches;e.addListener(t),t()}else sf.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||sf.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let e in this.projection&&this.projection.unmount(),y(this.notifyUpdate),y(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[e].clear();for(let e in this.features){let t=this.features[e];t&&(t.unmount(),t.isMounted=!1)}this.current=null}bindToMotionValue(e,t){let i;this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();let r=j.has(e);r&&this.onBindTransform&&this.onBindTransform();let n=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&v.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),s=t.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,e,t)),this.valueSubscriptions.set(e,()=>{n(),s(),i&&i(),t.owner&&t.stop()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}updateFeatures(){let e="animation";for(e in nO){let t=nO[e];if(!t)continue;let{isEnabled:i,Feature:r}=t;if(!this.features[e]&&r&&i(this.props)&&(this.features[e]=new r(this)),this.features[e]){let t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):i_()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let t=0;t<sy.length;t++){let i=sy[t];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let r=e["on"+i];r&&(this.propEventSubscriptions[i]=this.on(i,r))}this.prevMotionValues=function(e,t,i){for(let r in t){let n=t[r],s=i[r];if(D(n))e.addValue(r,n);else if(D(s))e.addValue(r,k(n,{owner:e}));else if(s!==n)if(e.hasValue(r)){let t=e.getValue(r);!0===t.liveStyle?t.jump(n):t.hasAnimated||t.set(n)}else{let t=e.getStaticValue(r);e.addValue(r,k(void 0!==t?t:n,{owner:e}))}}for(let r in i)void 0===t[r]&&e.removeValue(r);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){let t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){let i=this.values.get(e);t!==i&&(i&&this.removeValue(e),this.bindToMotionValue(e,t),this.values.set(e,t),this.latestValues[e]=t.get())}removeValue(e){this.values.delete(e);let t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let i=this.values.get(e);return void 0===i&&void 0!==t&&(i=k(null===t?void 0:t,{owner:this}),this.addValue(e,i)),i}readValue(e,t){let i=void 0===this.latestValues[e]&&this.current?this.getBaseTargetFromProps(this.props,e)??this.readValueFromInstance(this.current,e,this.options):this.latestValues[e];return null!=i&&("string"==typeof i&&(st(i)||sr(i))?i=parseFloat(i):!sm(i)&&eS.test(t)&&(i=sc(e,t)),this.setBaseTarget(e,D(i)?i.get():i)),D(i)?i.get():i}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){let t,{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let r=u(this.props,i,this.presenceContext?.custom);r&&(t=r[e])}if(i&&void 0!==t)return t;let r=this.getBaseTargetFromProps(this.props,e);return void 0===r||D(r)?void 0!==this.initialValues[e]&&void 0===t?void 0:this.baseTarget[e]:r}on(e,t){return this.events[e]||(this.events[e]=new S),this.events[e].add(t)}notify(e,...t){this.events[e]&&this.events[e].notify(...t)}}class sb extends sx{constructor(){super(...arguments),this.KeyframeResolver=sh}sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,{vars:t,style:i}){delete t[e],delete i[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:e}=this.props;D(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent=`${e}`)}))}}function sw(e,{style:t,vars:i},r,n){for(let s in Object.assign(e.style,t,n&&n.getProjectionStyles(r)),i)e.style.setProperty(s,i[s])}class sj extends sb{constructor(){super(...arguments),this.type="html",this.renderInstance=sw}readValueFromInstance(e,t){if(j.has(t))return this.projection?.isProjecting?tE(t):tS(e,t);{let i=window.getComputedStyle(e),r=(X(t)?i.getPropertyValue(t):i[t])||0;return"string"==typeof r?r.trim():r}}measureInstanceViewportBox(e,{transformPagePoint:t}){return iq(e,t)}build(e,t,i){nW(e,t,i.transformTemplate)}scrapeMotionValuesFromProps(e,t,i){return n4(e,t,i)}}let sP=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class sE extends sb{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=i_}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(j.has(t)){let e=su(t);return e&&e.default||0}return t=sP.has(t)?t:V(t),e.getAttribute(t)}scrapeMotionValuesFromProps(e,t,i){return n6(e,t,i)}build(e,t,i){nK(e,t,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(e,t,i,r){for(let i in sw(e,t,void 0,r),t.attrs)e.setAttribute(sP.has(i)?i:V(i),t.attrs[i])}mount(e){this.isSVGTag=nZ(e.tagName),super.mount(e)}}let sT=function(e){if("undefined"==typeof Proxy)return e;let t=new Map;return new Proxy((...t)=>e(...t),{get:(i,r)=>"create"===r?e:(t.has(r)||t.set(r,e(r)),t.get(r))})}((n={animation:{Feature:ip},exit:{Feature:ig},inView:{Feature:nS},tap:{Feature:nb},focus:{Feature:nd},hover:{Feature:nc},pan:{Feature:i7},drag:{Feature:i8,ProjectionNode:na,MeasureLayout:rc},layout:{ProjectionNode:na,MeasureLayout:rc}},s=(e,t)=>n2(e)?new sE(t):new sj(t,{allowProjection:e!==rt.Fragment}),function(e,{forwardMotionProps:t}={forwardMotionProps:!1}){return function({preloadedFeatures:e,createVisualElement:t,useRender:i,useVisualState:r,Component:n}){function s(e,s){var o,l,u;let c,d={...(0,rt.useContext)(nR),...e,layoutId:function({layoutId:e}){let t=(0,rt.useContext)(rr).id;return t&&void 0!==e?t+"-"+e:e}(e)},{isStatic:h}=d,p=function(e){let{initial:t,animate:i}=function(e,t){if(nN(e)){let{initial:t,animate:i}=e;return{initial:!1===t||ir(t)?t:void 0,animate:ir(i)?i:void 0}}return!1!==e.inherit?t:{}}(e,(0,rt.useContext)(nM));return(0,rt.useMemo)(()=>({initial:t,animate:i}),[nk(t),nk(i)])}(e),m=r(e,h);if(!h&&n_){l=0,u=0,(0,rt.useContext)(nA).strict;let e=function(e){let{drag:t,layout:i}=nO;if(!t&&!i)return{};let r={...t,...i};return{MeasureLayout:t?.isEnabled(e)||i?.isEnabled(e)?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}(d);c=e.MeasureLayout,p.visualElement=function(e,t,i,r,n){let{visualElement:s}=(0,rt.useContext)(nM),a=(0,rt.useContext)(nA),o=(0,rt.useContext)(ri),l=(0,rt.useContext)(nR).reducedMotion,u=(0,rt.useRef)(null);r=r||a.renderer,!u.current&&r&&(u.current=r(e,{visualState:t,parent:s,props:i,presenceContext:o,blockInitialAnimation:!!o&&!1===o.initial,reducedMotionConfig:l}));let c=u.current,d=(0,rt.useContext)(rn);c&&!c.projection&&n&&("html"===c.type||"svg"===c.type)&&function(e,t,i,r){let{layoutId:n,layout:s,drag:a,dragConstraints:o,layoutScroll:l,layoutRoot:u,layoutCrossfade:c}=t;e.projection=new i(e.latestValues,t["data-framer-portal-id"]?void 0:function e(t){if(t)return!1!==t.options.allowProjection?t.projection:e(t.parent)}(e.parent)),e.projection.setOptions({layoutId:n,layout:s,alwaysMeasureLayout:!!a||o&&iX(o),visualElement:e,animationType:"string"==typeof s?s:"both",initialPromotionConfig:r,crossfade:c,layoutScroll:l,layoutRoot:u})}(u.current,i,n,d);let h=(0,rt.useRef)(!1);(0,rt.useInsertionEffect)(()=>{c&&h.current&&c.update(i,o)});let p=i[L],m=(0,rt.useRef)(!!p&&!window.MotionHandoffIsComplete?.(p)&&window.MotionHasOptimisedAnimation?.(p));return nL(()=>{c&&(h.current=!0,window.MotionIsMounted=!0,c.updateFeatures(),re.render(c.render),m.current&&c.animationState&&c.animationState.animateChanges())}),(0,rt.useEffect)(()=>{c&&(!m.current&&c.animationState&&c.animationState.animateChanges(),m.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(p)}),m.current=!1))}),c}(n,m,d,t,e.ProjectionNode)}return(0,a.jsxs)(nM.Provider,{value:p,children:[c&&p.visualElement?(0,a.jsx)(c,{visualElement:p.visualElement,...d}):null,i(n,e,(o=p.visualElement,(0,rt.useCallback)(e=>{e&&m.onMount&&m.onMount(e),o&&(e?o.mount(e):o.unmount()),s&&("function"==typeof s?s(e):iX(s)&&(s.current=e))},[o])),m,h,p.visualElement)]})}e&&function(e){for(let t in e)nO[t]={...nO[t],...e[t]}}(e),s.displayName=`motion.${"string"==typeof n?n:`create(${n.displayName??n.name??""})`}`;let o=(0,rt.forwardRef)(s);return o[nV]=n,o}({...n2(e)?n8:n5,preloadedFeatures:n,useRender:function(e=!1){return(t,i,r,{latestValues:n},s)=>{let a=(n2(t)?function(e,t,i,r){let n=(0,rt.useMemo)(()=>{let i=nY();return nK(i,t,nZ(r),e.transformTemplate,e.style),{...i.attrs,style:{...i.style}}},[t]);if(e.style){let t={};nH(t,e.style,e),n.style={...t,...n.style}}return n}:function(e,t){let i={},r=function(e,t){let i=e.style||{},r={};return nH(r,i,e),Object.assign(r,function({transformTemplate:e},t){return(0,rt.useMemo)(()=>{let i=nq();return nW(i,t,e),Object.assign({},i.vars,i.style)},[t])}(e,t)),r}(e,t);return e.drag&&!1!==e.dragListener&&(i.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=!0===e.drag?"none":`pan-${"x"===e.drag?"y":"x"}`),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(i.tabIndex=0),i.style=r,i})(i,n,s,t),o=function(e,t,i){let r={};for(let n in e)("values"!==n||"object"!=typeof e.values)&&(n0(n)||!0===i&&nJ(n)||!t&&!nJ(n)||e.draggable&&n.startsWith("onDrag"))&&(r[n]=e[n]);return r}(i,"string"==typeof t,e),l=t!==rt.Fragment?{...o,...a,ref:r}:{},{children:u}=i,c=(0,rt.useMemo)(()=>D(u)?u.get():u,[u]);return(0,rt.createElement)(t,{...l,children:c})}}(t),createVisualElement:s,Component:e})})),sS=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),sA=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,i)=>i?i.toUpperCase():t.toLowerCase()),sR=e=>{let t=sA(e);return t.charAt(0).toUpperCase()+t.slice(1)},sM=(...e)=>e.filter((e,t,i)=>!!e&&""!==e.trim()&&i.indexOf(e)===t).join(" ").trim(),sN=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var sC={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let sk=(0,rt.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:i=2,absoluteStrokeWidth:r,className:n="",children:s,iconNode:a,...o},l)=>(0,rt.createElement)("svg",{ref:l,...sC,width:t,height:t,stroke:e,strokeWidth:r?24*Number(i)/Number(t):i,className:sM("lucide",n),...!s&&!sN(o)&&{"aria-hidden":"true"},...o},[...a.map(([e,t])=>(0,rt.createElement)(e,t)),...Array.isArray(s)?s:[s]])),s_=(e,t)=>{let i=(0,rt.forwardRef)(({className:i,...r},n)=>(0,rt.createElement)(sk,{ref:n,iconNode:t,className:sM(`lucide-${sS(sR(e))}`,`lucide-${e}`,i),...r}));return i.displayName=sR(e),i},sD=s_("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]]),sO=s_("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]]),sV=s_("code",[["path",{d:"m16 18 6-6-6-6",key:"eg8j8"}],["path",{d:"m8 6-6 6 6 6",key:"ppft3o"}]]),sL=s_("server",[["rect",{width:"20",height:"8",x:"2",y:"2",rx:"2",ry:"2",key:"ngkwjq"}],["rect",{width:"20",height:"8",x:"2",y:"14",rx:"2",ry:"2",key:"iecqi9"}],["line",{x1:"6",x2:"6.01",y1:"6",y2:"6",key:"16zg32"}],["line",{x1:"6",x2:"6.01",y1:"18",y2:"18",key:"nzw8ys"}]]),sI=s_("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]),sF=s_("smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]]),sU=s_("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]),s$=s_("github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]]),sB=s_("linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]]);var sz=i(1261),sW=i.n(sz);let sq=({name:e,logo:t})=>(0,a.jsxs)("div",{className:"flex items-center gap-2 p-2 bg-white/5 rounded-lg border border-white/10 hover:border-purple-400/30 transition-colors",children:[(0,a.jsx)("div",{className:"w-6 h-6 flex items-center justify-center",children:(0,a.jsx)("div",{className:"w-full h-full bg-contain bg-center bg-no-repeat",style:{backgroundImage:`url(${t})`}})}),(0,a.jsx)("span",{className:"text-gray-300 text-sm",children:e})]});function sH(){let e={initial:{opacity:0,y:60},animate:{opacity:1,y:0},transition:{duration:.6}};return(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900",children:[(0,a.jsx)("nav",{className:"fixed top-0 w-full bg-black/20 backdrop-blur-md z-50 border-b border-white/10",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex justify-between items-center py-4",children:[(0,a.jsx)(sT.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},className:"text-2xl font-bold text-white",children:"Gabriel Moreno"}),(0,a.jsx)(sT.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},className:"hidden md:flex space-x-8",children:["Inicio","Sobre m\xed","Habilidades","Proyectos","Experiencia","Contacto"].map(e=>(0,a.jsx)("a",{href:`#${e.toLowerCase().replace(" ","-")}`,className:"text-gray-300 hover:text-white transition-colors duration-200",children:e},e))})]})})}),(0,a.jsx)("section",{id:"inicio",className:"pt-20 pb-20 px-4 sm:px-6 lg:px-8",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto",children:(0,a.jsxs)(sT.div,{variants:{animate:{transition:{staggerChildren:.1}}},initial:"initial",animate:"animate",className:"text-center",children:[(0,a.jsx)(sT.div,{variants:e,className:"mb-8",children:(0,a.jsx)("div",{className:"w-32 h-32 mx-auto mb-6 rounded-full bg-gradient-to-r from-purple-400 to-pink-400 p-1",children:(0,a.jsxs)("div",{className:"w-full h-full rounded-full bg-gray-800 flex items-center justify-center overflow-hidden",children:[(0,a.jsx)(sW(),{src:"/profile.png",alt:"Gabriel Moreno",width:120,height:120,className:"w-full h-full object-cover rounded-full",onError:e=>{e.currentTarget.style.display="none";let t=e.currentTarget.nextElementSibling;t&&(t.style.display="flex")}}),(0,a.jsx)("span",{className:"text-4xl font-bold text-white hidden",children:"GM"})]})})}),(0,a.jsx)(sT.h1,{variants:e,className:"text-5xl md:text-7xl font-bold text-white mb-6",children:"Gabriel Moreno"}),(0,a.jsx)(sT.p,{variants:e,className:"text-xl md:text-2xl text-purple-300 mb-8",children:"Desarrollador Fullstack"}),(0,a.jsx)(sT.p,{variants:e,className:"text-lg text-gray-300 max-w-3xl mx-auto mb-12 leading-relaxed text-center",children:"Apasionado por la programaci\xf3n y en pleno proceso de formaci\xf3n y crecimiento. Gran motivaci\xf3n por aprender, mejorar y aportar valor desde el primer d\xeda."}),(0,a.jsxs)(sT.div,{variants:e,className:"flex flex-col sm:flex-row gap-6 justify-center items-center",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 text-gray-300",children:[(0,a.jsx)(sD,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:"+34 634668535"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 text-gray-300",children:[(0,a.jsx)(sO,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:"<EMAIL>"})]})]}),(0,a.jsx)(sT.div,{variants:e,className:"mt-8",children:(0,a.jsxs)("a",{href:"/Moreno Mu\xf1oz, Gabriel - Curriculum.pdf",download:"Moreno Mu\xf1oz, Gabriel - Curriculum.pdf",className:"inline-flex items-center gap-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white font-semibold py-4 px-8 rounded-lg hover:from-purple-600 hover:to-pink-600 transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl",children:[(0,a.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),"Descargar CV"]})})]})})}),(0,a.jsx)("section",{id:"sobre-m\xed",className:"py-20 px-4 sm:px-6 lg:px-8 bg-black/20",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,a.jsxs)(sT.div,{initial:{opacity:0,y:60},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"text-center mb-16",children:[(0,a.jsx)("h2",{className:"text-4xl md:text-5xl font-bold text-white mb-6",children:"Sobre m\xed"}),(0,a.jsx)("div",{className:"w-24 h-1 bg-gradient-to-r from-purple-400 to-pink-400 mx-auto"})]}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-12 items-center",children:[(0,a.jsxs)(sT.div,{initial:{opacity:0,x:-60},whileInView:{opacity:1,x:0},transition:{duration:.6},viewport:{once:!0},children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-white mb-6",children:"Mi Perfil Profesional"}),(0,a.jsx)("p",{className:"text-gray-300 mb-6 leading-relaxed text-justify",children:"Apasionado por la programaci\xf3n y en pleno proceso de formaci\xf3n y crecimiento. Gran motivaci\xf3n por aprender, mejorar y aportar valor desde el primer d\xeda."}),(0,a.jsxs)("p",{className:"text-gray-300 mb-6 leading-relaxed text-justify",children:["Me esfuerzo por dar siempre lo mejor de m\xed, soy ",(0,a.jsx)("strong",{className:"text-purple-300",children:"constante"}),", ",(0,a.jsx)("strong",{className:"text-purple-300",children:"responsable "})," y tengo muchas ganas de empezar a trabajar en un entorno profesional donde pueda desarrollarme y contribuir al equipo."]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-purple-400 rounded-full"}),(0,a.jsx)("span",{className:"text-gray-300",children:"Actitud proactiva y resolutiva"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-purple-400 rounded-full"}),(0,a.jsx)("span",{className:"text-gray-300",children:"Gran capacidad de aprendizaje"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-purple-400 rounded-full"}),(0,a.jsx)("span",{className:"text-gray-300",children:"Adaptabilidad a nuevas herramientas y metodolog\xedas"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-purple-400 rounded-full"}),(0,a.jsx)("span",{className:"text-gray-300",children:"Inter\xe9s por la mejora continua y el crecimiento profesional"})]})]})]}),(0,a.jsxs)(sT.div,{initial:{opacity:0,x:60},whileInView:{opacity:1,x:0},transition:{duration:.6},viewport:{once:!0},className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10",children:[(0,a.jsx)("h4",{className:"text-xl font-semibold text-white mb-4",children:"Educaci\xf3n"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-purple-300 font-medium",children:"Desarrollo de Aplicaciones Multiplataforma"}),(0,a.jsx)("p",{className:"text-gray-400",children:"IES GRAN CAPIT\xc1N • 2022-2024"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-purple-300 font-medium",children:"T\xe9cnico en Sistemas Microinform\xe1ticos y Redes"}),(0,a.jsx)("p",{className:"text-gray-400",children:"IES FIDIANA • 2020-2022"})]})]})]}),(0,a.jsxs)("div",{className:"bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10",children:[(0,a.jsx)("h4",{className:"text-xl font-semibold text-white mb-4",children:"Idiomas"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-300",children:"Espa\xf1ol"}),(0,a.jsx)("span",{className:"text-purple-300",children:"Nativo"})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-300",children:"Ingl\xe9s"}),(0,a.jsx)("span",{className:"text-purple-300",children:"B1"})]})]})]}),(0,a.jsxs)("div",{className:"bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10",children:[(0,a.jsx)("h4",{className:"text-xl font-semibold text-white mb-4",children:"Programa Talento Joven"}),(0,a.jsx)("p",{className:"text-gray-300 text-sm text-justify",children:"Mi contrataci\xf3n est\xe1 sujeta a una ayuda de 4.950 € por parte de la C\xe1mara de Comercio de C\xf3rdoba, por mi participaci\xf3n en su programa de cualificaci\xf3n y empleo 'Talento Joven'."})]})]})]})]})}),(0,a.jsx)("section",{id:"habilidades",className:"py-20 px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,a.jsxs)(sT.div,{initial:{opacity:0,y:60},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"text-center mb-16",children:[(0,a.jsx)("h2",{className:"text-4xl md:text-5xl font-bold text-white mb-6",children:"Habilidades T\xe9cnicas"}),(0,a.jsx)("div",{className:"w-24 h-1 bg-gradient-to-r from-purple-400 to-pink-400 mx-auto"})]}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-8",children:[(0,a.jsxs)(sT.div,{initial:{opacity:0,y:60},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1},viewport:{once:!0},className:"bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10 hover:border-purple-400/50 transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,a.jsx)(sV,{className:"w-8 h-8 text-purple-400"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-white",children:"Frontend"})]}),(0,a.jsx)("div",{className:"grid grid-cols-2 gap-2",children:[{name:"React",logo:"https://cdn.jsdelivr.net/gh/devicons/devicon/icons/react/react-original.svg"},{name:"Vue.js",logo:"https://cdn.jsdelivr.net/gh/devicons/devicon/icons/vuejs/vuejs-original.svg"},{name:"JavaScript",logo:"https://cdn.jsdelivr.net/gh/devicons/devicon/icons/javascript/javascript-original.svg"},{name:"TypeScript",logo:"https://cdn.jsdelivr.net/gh/devicons/devicon/icons/typescript/typescript-original.svg"},{name:"HTML5",logo:"https://cdn.jsdelivr.net/gh/devicons/devicon/icons/html5/html5-original.svg"},{name:"CSS3",logo:"https://cdn.jsdelivr.net/gh/devicons/devicon/icons/css3/css3-original.svg"}].map(e=>(0,a.jsx)(sq,{name:e.name,logo:e.logo},e.name))})]}),(0,a.jsxs)(sT.div,{initial:{opacity:0,y:60},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.2},viewport:{once:!0},className:"bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10 hover:border-purple-400/50 transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,a.jsx)(sL,{className:"w-8 h-8 text-purple-400"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-white",children:"Backend"})]}),(0,a.jsx)("div",{className:"grid grid-cols-2 gap-2",children:[{name:"Java",logo:"https://cdn.jsdelivr.net/gh/devicons/devicon/icons/java/java-original.svg"},{name:"Spring",logo:"https://cdn.jsdelivr.net/gh/devicons/devicon/icons/spring/spring-original.svg"},{name:"Node.js",logo:"https://cdn.jsdelivr.net/gh/devicons/devicon/icons/nodejs/nodejs-original.svg"},{name:"PHP",logo:"https://cdn.jsdelivr.net/gh/devicons/devicon/icons/php/php-original.svg"},{name:"Lua",logo:"https://cdn.jsdelivr.net/gh/devicons/devicon/icons/lua/lua-original.svg"}].map(e=>(0,a.jsx)(sq,{name:e.name,logo:e.logo},e.name))})]}),(0,a.jsxs)(sT.div,{initial:{opacity:0,y:60},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.3},viewport:{once:!0},className:"bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10 hover:border-purple-400/50 transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,a.jsx)(sI,{className:"w-8 h-8 text-purple-400"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-white",children:"Base de Datos"})]}),(0,a.jsx)("div",{className:"grid grid-cols-2 gap-2",children:[{name:"MySQL",logo:"https://cdn.jsdelivr.net/gh/devicons/devicon/icons/mysql/mysql-original.svg"},{name:"MariaDB",logo:"https://cdn.jsdelivr.net/gh/devicons/devicon/icons/mariadb/mariadb-original.svg"}].map(e=>(0,a.jsx)(sq,{name:e.name,logo:e.logo},e.name))})]}),(0,a.jsxs)(sT.div,{initial:{opacity:0,y:60},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.4},viewport:{once:!0},className:"bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10 hover:border-purple-400/50 transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,a.jsx)(sF,{className:"w-8 h-8 text-purple-400"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-white",children:"Herramientas"})]}),(0,a.jsx)("div",{className:"grid grid-cols-2 gap-2",children:[{name:"Git",logo:"https://cdn.jsdelivr.net/gh/devicons/devicon/icons/git/git-original.svg"},{name:"GitHub",logo:"https://cdn.jsdelivr.net/gh/devicons/devicon/icons/github/github-original.svg"},{name:"WordPress",logo:"https://cdn.jsdelivr.net/gh/devicons/devicon/icons/wordpress/wordpress-original.svg"},{name:"React Native",logo:"https://cdn.jsdelivr.net/gh/devicons/devicon/icons/react/react-original.svg"}].map(e=>(0,a.jsx)(sq,{name:e.name,logo:e.logo},e.name))})]})]})]})}),(0,a.jsx)("section",{id:"proyectos",className:"py-20 px-4 sm:px-6 lg:px-8 bg-black/20",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,a.jsxs)(sT.div,{initial:{opacity:0,y:60},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"text-center mb-16",children:[(0,a.jsx)("h2",{className:"text-4xl md:text-5xl font-bold text-white mb-6",children:"Proyectos Destacados"}),(0,a.jsx)("div",{className:"w-24 h-1 bg-gradient-to-r from-purple-400 to-pink-400 mx-auto"})]}),(0,a.jsx)("div",{className:"grid lg:grid-cols-1 gap-8",children:(0,a.jsx)(sT.div,{initial:{opacity:0,y:60},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"bg-white/5 backdrop-blur-sm rounded-lg p-8 border border-white/10 hover:border-purple-400/50 transition-all duration-300 hover:transform hover:scale-105",children:(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row gap-8",children:[(0,a.jsxs)("div",{className:"lg:w-2/3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,a.jsx)(sU,{className:"w-6 h-6 text-purple-400"}),(0,a.jsx)("h3",{className:"text-2xl font-bold text-white",children:"Servidor GTA V Roleplay (FiveM)"}),(0,a.jsx)("span",{className:"text-sm bg-purple-500/20 text-purple-300 px-3 py-1 rounded-full",children:"2024-2025"})]}),(0,a.jsx)("p",{className:"text-gray-300 mb-6 leading-relaxed text-justify",children:"Desarrollo completo de un servidor multijugador personalizado con arquitectura cliente-servidor, centrado en l\xf3gica de juego, sistemas persistentes y rendimiento en tiempo real.                  "}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-3",children:"Caracter\xedsticas principales:"}),(0,a.jsxs)("ul",{className:"space-y-2 text-gray-300",children:[(0,a.jsxs)("li",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-purple-400 rounded-full"}),"Programaci\xf3n de l\xf3gica backend en Lua y JavaScript sobre QB-Core (FiveM)."]}),(0,a.jsxs)("li",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-purple-400 rounded-full"}),"Modelado y gesti\xf3n de base de datos MySQL/MariaDB"]}),(0,a.jsxs)("li",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-purple-400 rounded-full"}),"Desarrollo de scripts persistentes: econom\xeda, inventario, crafting, permisos,..."]}),(0,a.jsxs)("li",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-purple-400 rounded-full"}),"Comunicaci\xf3n cliente-servidor asincr\xf3nica (eventos, callbacks, sincronizaci\xf3n)."]}),(0,a.jsxs)("li",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-purple-400 rounded-full"}),"Control de versiones y colaboraci\xf3n con GitHub."]})]})]})]}),(0,a.jsx)("div",{className:"lg:w-1/3",children:(0,a.jsxs)("div",{className:"bg-white/5 rounded-lg p-6 border border-white/10",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-4",children:"Tecnolog\xedas Utilizadas"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-purple-300 font-medium",children:"Lenguajes:"}),(0,a.jsx)("p",{className:"text-gray-300 text-sm",children:"Lua, JavaScript, SQL, HTML/CSS"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-purple-300 font-medium",children:"Frameworks:"}),(0,a.jsx)("p",{className:"text-gray-300 text-sm",children:"React, Vue.js, QB-Core"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-purple-300 font-medium",children:"Herramientas:"}),(0,a.jsx)("p",{className:"text-gray-300 text-sm",children:"GitHub, FXServer, VPS"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-purple-300 font-medium",children:"Infraestructura:"}),(0,a.jsx)("p",{className:"text-gray-300 text-sm",children:"VPS, MySQL"})]})]})]})})]})})})]})}),(0,a.jsx)("section",{id:"experiencia",className:"py-20 px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,a.jsxs)(sT.div,{initial:{opacity:0,y:60},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"text-center mb-16",children:[(0,a.jsx)("h2",{className:"text-4xl md:text-5xl font-bold text-white mb-6",children:"Experiencia Laboral"}),(0,a.jsx)("div",{className:"w-24 h-1 bg-gradient-to-r from-purple-400 to-pink-400 mx-auto"})]}),(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)(sT.div,{initial:{opacity:0,x:-60},whileInView:{opacity:1,x:0},transition:{duration:.6},viewport:{once:!0},className:"bg-white/5 backdrop-blur-sm rounded-lg p-8 border border-white/10 hover:border-purple-400/50 transition-colors",children:[(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-white mb-2",children:"Desarrollador Multiplataforma (FCT)"}),(0,a.jsx)("h4",{className:"text-xl text-purple-300 mb-2",children:"AICOR Consultores Inform\xe1ticos"}),(0,a.jsx)("span",{className:"text-gray-400",children:"2024"})]}),(0,a.jsx)("div",{className:"mt-4 lg:mt-0",children:(0,a.jsx)("span",{className:"bg-green-500/20 text-green-300 px-3 py-1 rounded-full text-sm",children:"Formaci\xf3n en Centros de Trabajo"})})]}),(0,a.jsx)("p",{className:"text-gray-300 mb-4 leading-relaxed text-justify",children:"Desarrollo de aplicaciones multiplataforma utilizando tecnolog\xedas modernas. Particip\xe9 en proyectos reales trabajando con frameworks y herramientas de desarrollo actuales."}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-lg font-semibold text-white mb-3",children:"Responsabilidades:"}),(0,a.jsxs)("ul",{className:"space-y-2 text-gray-300",children:[(0,a.jsxs)("li",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-purple-400 rounded-full"}),"Desarrollo con React Native"]}),(0,a.jsxs)("li",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-purple-400 rounded-full"}),"Desarrollo frontend con React"]}),(0,a.jsxs)("li",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-purple-400 rounded-full"}),"Desarrollo y mantenimiento en WordPress"]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-lg font-semibold text-white mb-3",children:"Tecnolog\xedas:"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:["React","React Native","WordPress","PHP","JavaScript"].map(e=>(0,a.jsx)("span",{className:"bg-purple-500/20 text-purple-300 px-3 py-1 rounded-full text-sm",children:e},e))})]})]})]}),(0,a.jsxs)(sT.div,{initial:{opacity:0,x:60},whileInView:{opacity:1,x:0},transition:{duration:.6},viewport:{once:!0},className:"bg-white/5 backdrop-blur-sm rounded-lg p-8 border border-white/10 hover:border-purple-400/50 transition-colors",children:[(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-white mb-2",children:"T\xe9cnico Auxiliar Inform\xe1tico (FCT)"}),(0,a.jsx)("h4",{className:"text-xl text-purple-300 mb-2",children:"Ayuntamiento de C\xf3rdoba"}),(0,a.jsx)("span",{className:"text-gray-400",children:"2022"})]}),(0,a.jsx)("div",{className:"mt-4 lg:mt-0",children:(0,a.jsx)("span",{className:"bg-green-500/20 text-green-300 px-3 py-1 rounded-full text-sm",children:"Formaci\xf3n en Centros de Trabajo"})})]}),(0,a.jsx)("p",{className:"text-gray-300 mb-4 leading-relaxed text-justify",children:"Experiencia en el mantenimiento y soporte t\xe9cnico de equipos inform\xe1ticos en el sector p\xfablico. Trabajo con hardware y sistemas de la administraci\xf3n local."}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-lg font-semibold text-white mb-3",children:"Responsabilidades:"}),(0,a.jsxs)("ul",{className:"space-y-2 text-gray-300",children:[(0,a.jsxs)("li",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-purple-400 rounded-full"}),"Reparaci\xf3n de equipos inform\xe1ticos"]}),(0,a.jsxs)("li",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-purple-400 rounded-full"}),"Mantenimiento preventivo y correctivo"]}),(0,a.jsxs)("li",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-purple-400 rounded-full"}),"Clonaci\xf3n y configuraci\xf3n de equipos"]}),(0,a.jsxs)("li",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-purple-400 rounded-full"}),"Soporte t\xe9cnico a usuarios"]})]})]})]})]})]})}),(0,a.jsx)("section",{id:"contacto",className:"py-20 px-4 sm:px-6 lg:px-8 bg-black/20",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,a.jsxs)(sT.div,{initial:{opacity:0,y:60},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"text-center mb-16",children:[(0,a.jsx)("h2",{className:"text-4xl md:text-5xl font-bold text-white mb-6",children:"Contacto"}),(0,a.jsx)("div",{className:"w-24 h-1 bg-gradient-to-r from-purple-400 to-pink-400 mx-auto mb-6"}),(0,a.jsx)("p",{className:"text-xl text-gray-300 max-w-3xl mx-auto text-center",children:"\xbfTienes un proyecto en mente? \xa1Me encantar\xeda escuchar sobre \xe9l y ver c\xf3mo puedo ayudarte!"})]}),(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsxs)(sT.div,{initial:{opacity:0,y:60},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"max-w-2xl w-full",children:[(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-8",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4 p-6 bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 hover:border-purple-400/50 transition-colors",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center",children:(0,a.jsx)(sO,{className:"w-6 h-6 text-purple-400"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Email"}),(0,a.jsx)("a",{href:"mailto:<EMAIL>",className:"text-purple-300 hover:text-purple-200 transition-colors",children:"<EMAIL>"})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-4 p-6 bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 hover:border-purple-400/50 transition-colors",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center",children:(0,a.jsx)(sD,{className:"w-6 h-6 text-purple-400"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Tel\xe9fono"}),(0,a.jsx)("a",{href:"tel:+34634668535",className:"text-purple-300 hover:text-purple-200 transition-colors",children:"+34 634 668 535"})]})]})]}),(0,a.jsxs)("div",{className:"flex justify-center gap-4 mt-8",children:[(0,a.jsx)("a",{href:"https://github.com/gabrielmordev",target:"_blank",rel:"noopener noreferrer",className:"w-12 h-12 bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 hover:border-purple-400/50 flex items-center justify-center transition-colors group",children:(0,a.jsx)(s$,{className:"w-6 h-6 text-gray-400 group-hover:text-purple-400 transition-colors"})}),(0,a.jsx)("a",{href:"https://www.linkedin.com/in/gabriel-moreno-munoz/",target:"_blank",rel:"noopener noreferrer",className:"w-12 h-12 bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 hover:border-purple-400/50 flex items-center justify-center transition-colors group",children:(0,a.jsx)(sB,{className:"w-6 h-6 text-gray-400 group-hover:text-purple-400 transition-colors"})})]})]})})]})}),(0,a.jsx)("footer",{className:"py-8 px-4 sm:px-6 lg:px-8 border-t border-white/10",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto text-center",children:(0,a.jsx)("p",{className:"text-gray-400",children:"\xa9 2025 Gabriel Moreno. Desarrollado con Next.js y Tailwind CSS."})})})]})}},8911:(e,t,i)=>{Promise.resolve().then(i.t.bind(i,6444,23)),Promise.resolve().then(i.t.bind(i,6042,23)),Promise.resolve().then(i.t.bind(i,8170,23)),Promise.resolve().then(i.t.bind(i,9477,23)),Promise.resolve().then(i.t.bind(i,9345,23)),Promise.resolve().then(i.t.bind(i,2089,23)),Promise.resolve().then(i.t.bind(i,6577,23)),Promise.resolve().then(i.t.bind(i,1307,23))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9148:(e,t,i)=>{"use strict";e.exports=i(4041).vendored.contexts.RouterContext},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9513:(e,t,i)=>{"use strict";e.exports=i(4041).vendored.contexts.HeadManagerContext},9551:e=>{"use strict";e.exports=require("url")}};var t=require("../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),r=t.X(0,[447,145],()=>i(689));module.exports=r})();