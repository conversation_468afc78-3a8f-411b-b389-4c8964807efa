# Cómo añadir tu CV al portfolio

## 📄 Instrucciones para añadir tu currículum

Para que el botón de descarga del CV funcione correctamente, sigue estos pasos:

### 1. Preparar tu CV
- **Formato recomendado**: PDF
- **Nombre del archivo**: `cv-gabriel-moreno.pdf`
- **Tamaño**: Preferiblemente menos de 5MB

### 2. Añadir el CV al proyecto
1. Coloca tu archivo PDF en la carpeta `public/` del proyecto
2. Asegúrate de que se llame exactamente `cv-gabriel-moreno.pdf`

### 3. Si usas un nombre diferente
Si quieres usar un nombre diferente para tu CV, edita el archivo `src/app/page.tsx` en la línea que contiene:
```jsx
href="/cv-gabriel-moreno.pdf"
download="CV-Gabriel-Moreno.pdf"
```

Y cámbialo por el nombre de tu archivo:
```jsx
href="/tu-cv.pdf"
download="Tu-CV.pdf"
```

### 4. Estructura de archivos

```
portfolio/
├── public/
│   ├── cv-gabriel-moreno.pdf   ← Tu CV aquí
│   ├── profile.jpg             ← Tu imagen de perfil
│   ├── next.svg
│   └── ...
├── src/
│   └── app/
│       └── page.tsx
└── ...
```

### 5. Verificar funcionamiento

Una vez que añadas tu CV:
1. El botón "Descargar CV" aparecerá en la sección hero
2. Al hacer clic, se descargará automáticamente tu CV
3. El archivo se descargará con el nombre especificado en el atributo `download`

## 🎨 Personalización del botón

El botón tiene un diseño atractivo con:
- Gradiente púrpura a rosa
- Icono de descarga
- Efecto hover con escala
- Animación suave

¡Una vez que añadas tu CV, los visitantes podrán descargarlo fácilmente desde tu portfolio!
