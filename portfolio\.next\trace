[{"name": "generate-buildid", "duration": 216, "timestamp": 308218833187, "id": 4, "parentId": 1, "tags": {}, "startTime": 1750158400733, "traceId": "e9de6b0f5aa10863"}, {"name": "load-custom-routes", "duration": 331, "timestamp": 308218833507, "id": 5, "parentId": 1, "tags": {}, "startTime": 1750158400733, "traceId": "e9de6b0f5aa10863"}, {"name": "create-dist-dir", "duration": 457, "timestamp": 308218918988, "id": 6, "parentId": 1, "tags": {}, "startTime": 1750158400819, "traceId": "e9de6b0f5aa10863"}, {"name": "create-pages-mapping", "duration": 307, "timestamp": 308218931770, "id": 7, "parentId": 1, "tags": {}, "startTime": 1750158400832, "traceId": "e9de6b0f5aa10863"}, {"name": "collect-app-paths", "duration": 2323, "timestamp": 308218932138, "id": 8, "parentId": 1, "tags": {}, "startTime": 1750158400832, "traceId": "e9de6b0f5aa10863"}, {"name": "create-app-mapping", "duration": 2162, "timestamp": 308218934503, "id": 9, "parentId": 1, "tags": {}, "startTime": 1750158400834, "traceId": "e9de6b0f5aa10863"}, {"name": "public-dir-conflict-check", "duration": 743, "timestamp": 308218937145, "id": 10, "parentId": 1, "tags": {}, "startTime": 1750158400837, "traceId": "e9de6b0f5aa10863"}, {"name": "generate-routes-manifest", "duration": 4473, "timestamp": 308218938388, "id": 11, "parentId": 1, "tags": {}, "startTime": 1750158400838, "traceId": "e9de6b0f5aa10863"}, {"name": "create-entrypoints", "duration": 24055, "timestamp": 308219898466, "id": 15, "parentId": 13, "tags": {}, "startTime": 1750158401798, "traceId": "e9de6b0f5aa10863"}, {"name": "generate-webpack-config", "duration": 565825, "timestamp": 308219922705, "id": 16, "parentId": 14, "tags": {}, "startTime": 1750158401823, "traceId": "e9de6b0f5aa10863"}, {"name": "next-trace-entrypoint-plugin", "duration": 2403, "timestamp": 308220622561, "id": 18, "parentId": 17, "tags": {}, "startTime": 1750158402523, "traceId": "e9de6b0f5aa10863"}, {"name": "add-entry", "duration": 259521, "timestamp": 308220632104, "id": 21, "parentId": 19, "tags": {"request": "next/dist/pages/_app"}, "startTime": 1750158402532, "traceId": "e9de6b0f5aa10863"}, {"name": "add-entry", "duration": 288274, "timestamp": 308220632316, "id": 22, "parentId": 19, "tags": {"request": "next-route-loader?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=next%2Fdist%2Fpages%2F_error&absoluteAppPath=next%2Fdist%2Fpages%2F_app&absoluteDocumentPath=next%2Fdist%2Fpages%2F_document&middlewareConfigBase64=e30%3D!"}, "startTime": 1750158402532, "traceId": "e9de6b0f5aa10863"}, {"name": "add-entry", "duration": 316876, "timestamp": 308220632384, "id": 25, "parentId": 19, "tags": {"request": "next/dist/pages/_document"}, "startTime": 1750158402532, "traceId": "e9de6b0f5aa10863"}, {"name": "add-entry", "duration": 336265, "timestamp": 308220632347, "id": 23, "parentId": 19, "tags": {"request": "next-app-loader?page=%2F_not-found%2Fpage&name=app%2F_not-found%2Fpage&pagePath=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&appDir=C%3A%5CUsers%5CPropietario%5CDesktop%5Cgabrielmordev%5Cportfolio%5Csrc%5Capp&appPaths=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750158402532, "traceId": "e9de6b0f5aa10863"}, {"name": "add-entry", "duration": 352175, "timestamp": 308220631358, "id": 20, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Ffavicon.ico%2Froute&name=app%2Ffavicon.ico%2Froute&pagePath=private-next-app-dir%2Ffavicon.ico&appDir=C%3A%5CUsers%5CPropietario%5CDesktop%5Cgabrielmordev%5Cportfolio%5Csrc%5Capp&appPaths=%2Ffavicon.ico&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750158402531, "traceId": "e9de6b0f5aa10863"}, {"name": "add-entry", "duration": 351690, "timestamp": 308220632368, "id": 24, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fpage&name=app%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CPropietario%5CDesktop%5Cgabrielmordev%5Cportfolio%5Csrc%5Capp&appPaths=%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750158402532, "traceId": "e9de6b0f5aa10863"}, {"name": "make", "duration": 703508, "timestamp": 308220631001, "id": 19, "parentId": 17, "tags": {}, "startTime": 1750158402531, "traceId": "e9de6b0f5aa10863"}, {"name": "get-entries", "duration": 1598, "timestamp": 308221336364, "id": 37, "parentId": 36, "tags": {}, "startTime": 1750158403236, "traceId": "e9de6b0f5aa10863"}, {"name": "node-file-trace-plugin", "duration": 73095, "timestamp": 308221342049, "id": 38, "parentId": 36, "tags": {"traceEntryCount": "8"}, "startTime": 1750158403242, "traceId": "e9de6b0f5aa10863"}, {"name": "collect-traced-files", "duration": 452, "timestamp": 308221415155, "id": 39, "parentId": 36, "tags": {}, "startTime": 1750158403315, "traceId": "e9de6b0f5aa10863"}, {"name": "finish-modules", "duration": 79667, "timestamp": 308221335946, "id": 36, "parentId": 18, "tags": {}, "startTime": 1750158403236, "traceId": "e9de6b0f5aa10863"}, {"name": "chunk-graph", "duration": 11987, "timestamp": 308221460134, "id": 41, "parentId": 40, "tags": {}, "startTime": 1750158403360, "traceId": "e9de6b0f5aa10863"}, {"name": "optimize-modules", "duration": 48, "timestamp": 308221472339, "id": 43, "parentId": 40, "tags": {}, "startTime": 1750158403372, "traceId": "e9de6b0f5aa10863"}, {"name": "optimize-chunks", "duration": 11636, "timestamp": 308221472528, "id": 44, "parentId": 40, "tags": {}, "startTime": 1750158403373, "traceId": "e9de6b0f5aa10863"}, {"name": "optimize-tree", "duration": 148, "timestamp": 308221484269, "id": 45, "parentId": 40, "tags": {}, "startTime": 1750158403384, "traceId": "e9de6b0f5aa10863"}, {"name": "optimize-chunk-modules", "duration": 22231, "timestamp": 308221484523, "id": 46, "parentId": 40, "tags": {}, "startTime": 1750158403385, "traceId": "e9de6b0f5aa10863"}, {"name": "optimize", "duration": 34631, "timestamp": 308221472238, "id": 42, "parentId": 40, "tags": {}, "startTime": 1750158403372, "traceId": "e9de6b0f5aa10863"}, {"name": "module-hash", "duration": 18534, "timestamp": 308221522058, "id": 47, "parentId": 40, "tags": {}, "startTime": 1750158403422, "traceId": "e9de6b0f5aa10863"}, {"name": "code-generation", "duration": 12758, "timestamp": 308221540674, "id": 48, "parentId": 40, "tags": {}, "startTime": 1750158403441, "traceId": "e9de6b0f5aa10863"}, {"name": "hash", "duration": 8728, "timestamp": 308221558781, "id": 49, "parentId": 40, "tags": {}, "startTime": 1750158403459, "traceId": "e9de6b0f5aa10863"}, {"name": "code-generation-jobs", "duration": 289, "timestamp": 308221567505, "id": 50, "parentId": 40, "tags": {}, "startTime": 1750158403467, "traceId": "e9de6b0f5aa10863"}, {"name": "module-assets", "duration": 404, "timestamp": 308221567745, "id": 51, "parentId": 40, "tags": {}, "startTime": 1750158403468, "traceId": "e9de6b0f5aa10863"}, {"name": "create-chunk-assets", "duration": 5549, "timestamp": 308221568168, "id": 52, "parentId": 40, "tags": {}, "startTime": 1750158403468, "traceId": "e9de6b0f5aa10863"}, {"name": "minify-js", "duration": 5321, "timestamp": 308221591125, "id": 55, "parentId": 53, "tags": {"name": "../pages/_app.js", "cache": "HIT"}, "startTime": 1750158403491, "traceId": "e9de6b0f5aa10863"}, {"name": "minify-js", "duration": 4382, "timestamp": 308221592073, "id": 57, "parentId": 53, "tags": {"name": "../app/_not-found/page.js", "cache": "HIT"}, "startTime": 1750158403492, "traceId": "e9de6b0f5aa10863"}, {"name": "minify-js", "duration": 4368, "timestamp": 308221592092, "id": 58, "parentId": 53, "tags": {"name": "../app/page.js", "cache": "HIT"}, "startTime": 1750158403492, "traceId": "e9de6b0f5aa10863"}, {"name": "minify-js", "duration": 4296, "timestamp": 308221592167, "id": 59, "parentId": 53, "tags": {"name": "../pages/_document.js", "cache": "HIT"}, "startTime": 1750158403492, "traceId": "e9de6b0f5aa10863"}, {"name": "minify-js", "duration": 4245, "timestamp": 308221592218, "id": 60, "parentId": 53, "tags": {"name": "../webpack-runtime.js", "cache": "HIT"}, "startTime": 1750158403492, "traceId": "e9de6b0f5aa10863"}, {"name": "minify-js", "duration": 4226, "timestamp": 308221592241, "id": 61, "parentId": 53, "tags": {"name": "447.js", "cache": "HIT"}, "startTime": 1750158403492, "traceId": "e9de6b0f5aa10863"}, {"name": "minify-js", "duration": 51, "timestamp": 308221596417, "id": 63, "parentId": 53, "tags": {"name": "548.js", "cache": "HIT"}, "startTime": 1750158403496, "traceId": "e9de6b0f5aa10863"}, {"name": "minify-js", "duration": 13962, "timestamp": 308221591226, "id": 56, "parentId": 53, "tags": {"name": "../pages/_error.js", "cache": "MISS"}, "startTime": 1750158403491, "traceId": "e9de6b0f5aa10863"}, {"name": "minify-js", "duration": 50281, "timestamp": 308221586900, "id": 54, "parentId": 53, "tags": {"name": "../app/favicon.ico/route.js", "cache": "MISS"}, "startTime": 1750158403487, "traceId": "e9de6b0f5aa10863"}, {"name": "minify-js", "duration": 108440, "timestamp": 308221592253, "id": 62, "parentId": 53, "tags": {"name": "145.js", "cache": "MISS"}, "startTime": 1750158403492, "traceId": "e9de6b0f5aa10863"}, {"name": "minify-webpack-plugin-optimize", "duration": 123016, "timestamp": 308221577691, "id": 53, "parentId": 17, "tags": {"compilationName": "server", "mangle": "true"}, "startTime": 1750158403478, "traceId": "e9de6b0f5aa10863"}, {"name": "css-minimizer-plugin", "duration": 179, "timestamp": 308221700906, "id": 64, "parentId": 17, "tags": {}, "startTime": 1750158403601, "traceId": "e9de6b0f5aa10863"}, {"name": "create-trace-assets", "duration": 1436, "timestamp": 308221701327, "id": 65, "parentId": 18, "tags": {}, "startTime": 1750158403601, "traceId": "e9de6b0f5aa10863"}, {"name": "seal", "duration": 270451, "timestamp": 308221440308, "id": 40, "parentId": 17, "tags": {}, "startTime": 1750158403340, "traceId": "e9de6b0f5aa10863"}, {"name": "webpack-compilation", "duration": 1096701, "timestamp": 308220620554, "id": 17, "parentId": 14, "tags": {"name": "server"}, "startTime": 1750158402521, "traceId": "e9de6b0f5aa10863"}, {"name": "emit", "duration": 7625, "timestamp": 308221717866, "id": 66, "parentId": 14, "tags": {}, "startTime": 1750158403618, "traceId": "e9de6b0f5aa10863"}, {"name": "webpack-close", "duration": 299247, "timestamp": 308221728322, "id": 67, "parentId": 14, "tags": {"name": "server"}, "startTime": 1750158403628, "traceId": "e9de6b0f5aa10863"}, {"name": "webpack-generate-error-stats", "duration": 3546, "timestamp": 308222027651, "id": 68, "parentId": 67, "tags": {}, "startTime": 1750158403928, "traceId": "e9de6b0f5aa10863"}, {"name": "run-webpack-compiler", "duration": 2133139, "timestamp": 308219898454, "id": 14, "parentId": 13, "tags": {}, "startTime": 1750158401798, "traceId": "e9de6b0f5aa10863"}, {"name": "format-webpack-messages", "duration": 170, "timestamp": 308222031606, "id": 69, "parentId": 13, "tags": {}, "startTime": 1750158403932, "traceId": "e9de6b0f5aa10863"}, {"name": "worker-main-server", "duration": 2134152, "timestamp": 308219897927, "id": 13, "parentId": 1, "tags": {}, "startTime": 1750158401798, "traceId": "e9de6b0f5aa10863"}, {"name": "create-entrypoints", "duration": 22604, "timestamp": 308223146643, "id": 73, "parentId": 71, "tags": {}, "startTime": 1750158405046, "traceId": "e9de6b0f5aa10863"}, {"name": "generate-webpack-config", "duration": 433588, "timestamp": 308223169441, "id": 74, "parentId": 72, "tags": {}, "startTime": 1750158405069, "traceId": "e9de6b0f5aa10863"}, {"name": "make", "duration": 945, "timestamp": 308223746218, "id": 76, "parentId": 75, "tags": {}, "startTime": 1750158405646, "traceId": "e9de6b0f5aa10863"}, {"name": "chunk-graph", "duration": 751, "timestamp": 308223750764, "id": 78, "parentId": 77, "tags": {}, "startTime": 1750158405650, "traceId": "e9de6b0f5aa10863"}, {"name": "optimize-modules", "duration": 42, "timestamp": 308223751741, "id": 80, "parentId": 77, "tags": {}, "startTime": 1750158405651, "traceId": "e9de6b0f5aa10863"}, {"name": "optimize-chunks", "duration": 1189, "timestamp": 308223751906, "id": 81, "parentId": 77, "tags": {}, "startTime": 1750158405651, "traceId": "e9de6b0f5aa10863"}, {"name": "optimize-tree", "duration": 148, "timestamp": 308223753192, "id": 82, "parentId": 77, "tags": {}, "startTime": 1750158405653, "traceId": "e9de6b0f5aa10863"}, {"name": "optimize-chunk-modules", "duration": 661, "timestamp": 308223753613, "id": 83, "parentId": 77, "tags": {}, "startTime": 1750158405653, "traceId": "e9de6b0f5aa10863"}, {"name": "optimize", "duration": 2785, "timestamp": 308223751605, "id": 79, "parentId": 77, "tags": {}, "startTime": 1750158405651, "traceId": "e9de6b0f5aa10863"}, {"name": "module-hash", "duration": 118, "timestamp": 308223755743, "id": 84, "parentId": 77, "tags": {}, "startTime": 1750158405655, "traceId": "e9de6b0f5aa10863"}, {"name": "code-generation", "duration": 263, "timestamp": 308223755919, "id": 85, "parentId": 77, "tags": {}, "startTime": 1750158405655, "traceId": "e9de6b0f5aa10863"}, {"name": "hash", "duration": 492, "timestamp": 308223756495, "id": 86, "parentId": 77, "tags": {}, "startTime": 1750158405656, "traceId": "e9de6b0f5aa10863"}, {"name": "code-generation-jobs", "duration": 167, "timestamp": 308223756983, "id": 87, "parentId": 77, "tags": {}, "startTime": 1750158405656, "traceId": "e9de6b0f5aa10863"}, {"name": "module-assets", "duration": 112, "timestamp": 308223757104, "id": 88, "parentId": 77, "tags": {}, "startTime": 1750158405657, "traceId": "e9de6b0f5aa10863"}, {"name": "create-chunk-assets", "duration": 233, "timestamp": 308223757227, "id": 89, "parentId": 77, "tags": {}, "startTime": 1750158405657, "traceId": "e9de6b0f5aa10863"}, {"name": "minify-js", "duration": 240, "timestamp": 308223771044, "id": 91, "parentId": 90, "tags": {"name": "interception-route-rewrite-manifest.js", "cache": "HIT"}, "startTime": 1750158405670, "traceId": "e9de6b0f5aa10863"}, {"name": "minify-webpack-plugin-optimize", "duration": 3575, "timestamp": 308223767727, "id": 90, "parentId": 75, "tags": {"compilationName": "edge-server", "mangle": "true"}, "startTime": 1750158405667, "traceId": "e9de6b0f5aa10863"}, {"name": "css-minimizer-plugin", "duration": 147, "timestamp": 308223771419, "id": 92, "parentId": 75, "tags": {}, "startTime": 1750158405671, "traceId": "e9de6b0f5aa10863"}, {"name": "seal", "duration": 25933, "timestamp": 308223749992, "id": 77, "parentId": 75, "tags": {}, "startTime": 1750158405649, "traceId": "e9de6b0f5aa10863"}, {"name": "webpack-compilation", "duration": 39171, "timestamp": 308223737166, "id": 75, "parentId": 72, "tags": {"name": "edge-server"}, "startTime": 1750158405637, "traceId": "e9de6b0f5aa10863"}, {"name": "emit", "duration": 4004, "timestamp": 308223776780, "id": 93, "parentId": 72, "tags": {}, "startTime": 1750158405676, "traceId": "e9de6b0f5aa10863"}, {"name": "webpack-close", "duration": 1000, "timestamp": 308223782217, "id": 94, "parentId": 72, "tags": {"name": "edge-server"}, "startTime": 1750158405682, "traceId": "e9de6b0f5aa10863"}, {"name": "webpack-generate-error-stats", "duration": 3468, "timestamp": 308223783286, "id": 95, "parentId": 94, "tags": {}, "startTime": 1750158405683, "traceId": "e9de6b0f5aa10863"}, {"name": "run-webpack-compiler", "duration": 640296, "timestamp": 308223146624, "id": 72, "parentId": 71, "tags": {}, "startTime": 1750158405046, "traceId": "e9de6b0f5aa10863"}, {"name": "format-webpack-messages", "duration": 87, "timestamp": 308223786927, "id": 96, "parentId": 71, "tags": {}, "startTime": 1750158405686, "traceId": "e9de6b0f5aa10863"}, {"name": "worker-main-edge-server", "duration": 641220, "timestamp": 308223145960, "id": 71, "parentId": 1, "tags": {}, "startTime": 1750158405046, "traceId": "e9de6b0f5aa10863"}, {"name": "create-entrypoints", "duration": 22761, "timestamp": 308224810528, "id": 99, "parentId": 97, "tags": {}, "startTime": 1750158406710, "traceId": "e9de6b0f5aa10863"}, {"name": "generate-webpack-config", "duration": 423712, "timestamp": 308224833484, "id": 100, "parentId": 98, "tags": {}, "startTime": 1750158406733, "traceId": "e9de6b0f5aa10863"}, {"name": "add-entry", "duration": 315543, "timestamp": 308225396964, "id": 106, "parentId": 102, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&page=%2F_not-found%2Fpage!"}, "startTime": 1750158407297, "traceId": "e9de6b0f5aa10863"}, {"name": "add-entry", "duration": 354134, "timestamp": 308225397122, "id": 108, "parentId": 102, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fpages%2F_app&page=%2F_app!"}, "startTime": 1750158407297, "traceId": "e9de6b0f5aa10863"}, {"name": "add-entry", "duration": 354194, "timestamp": 308225397090, "id": 107, "parentId": 102, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fpages%2F_error&page=%2F_error!"}, "startTime": 1750158407297, "traceId": "e9de6b0f5aa10863"}, {"name": "add-entry", "duration": 354179, "timestamp": 308225397159, "id": 110, "parentId": 102, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPropietario%5C%5CDesktop%5C%5Cgabrielmordev%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPropietario%5C%5CDesktop%5C%5Cgabrielmordev%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPropietario%5C%5CDesktop%5C%5Cgabrielmordev%5C%5Cportfolio%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"}, "startTime": 1750158407297, "traceId": "e9de6b0f5aa10863"}, {"name": "add-entry", "duration": 404074, "timestamp": 308225397145, "id": 109, "parentId": 102, "tags": {"request": "C:\\Users\\<USER>\\Desktop\\gabrielmordev\\portfolio\\node_modules\\next\\dist\\client\\router.js"}, "startTime": 1750158407297, "traceId": "e9de6b0f5aa10863"}, {"name": "add-entry", "duration": 435817, "timestamp": 308225396931, "id": 105, "parentId": 102, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPropietario%5C%5CDesktop%5C%5Cgabrielmordev%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPropietario%5C%5CDesktop%5C%5Cgabrielmordev%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPropietario%5C%5CDesktop%5C%5Cgabrielmordev%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPropietario%5C%5CDesktop%5C%5Cgabrielmordev%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPropietario%5C%5CDesktop%5C%5Cgabrielmordev%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPropietario%5C%5CDesktop%5C%5Cgabrielmordev%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPropietario%5C%5CDesktop%5C%5Cgabrielmordev%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPropietario%5C%5CDesktop%5C%5Cgabrielmordev%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"}, "startTime": 1750158407297, "traceId": "e9de6b0f5aa10863"}, {"name": "add-entry", "duration": 475473, "timestamp": 308225396892, "id": 104, "parentId": 102, "tags": {"request": "./node_modules/next/dist/client/app-next.js"}, "startTime": 1750158407297, "traceId": "e9de6b0f5aa10863"}, {"name": "add-entry", "duration": 568618, "timestamp": 308225396295, "id": 103, "parentId": 102, "tags": {"request": "./node_modules/next/dist/client/next.js"}, "startTime": 1750158407296, "traceId": "e9de6b0f5aa10863"}, {"name": "add-entry", "duration": 611930, "timestamp": 308225397170, "id": 111, "parentId": 102, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPropietario%5C%5CDesktop%5C%5Cgabrielmordev%5C%5Cportfolio%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"}, "startTime": 1750158407297, "traceId": "e9de6b0f5aa10863"}, {"name": "make", "duration": 613731, "timestamp": 308225395784, "id": 102, "parentId": 101, "tags": {}, "startTime": 1750158407296, "traceId": "e9de6b0f5aa10863"}, {"name": "chunk-graph", "duration": 13346, "timestamp": 308226059240, "id": 113, "parentId": 112, "tags": {}, "startTime": 1750158407959, "traceId": "e9de6b0f5aa10863"}, {"name": "optimize-modules", "duration": 44, "timestamp": 308226072772, "id": 115, "parentId": 112, "tags": {}, "startTime": 1750158407973, "traceId": "e9de6b0f5aa10863"}, {"name": "optimize-chunks", "duration": 12197, "timestamp": 308226075649, "id": 117, "parentId": 112, "tags": {}, "startTime": 1750158407975, "traceId": "e9de6b0f5aa10863"}, {"name": "optimize-tree", "duration": 146, "timestamp": 308226088022, "id": 118, "parentId": 112, "tags": {}, "startTime": 1750158407988, "traceId": "e9de6b0f5aa10863"}, {"name": "optimize-chunk-modules", "duration": 25573, "timestamp": 308226088275, "id": 119, "parentId": 112, "tags": {}, "startTime": 1750158407988, "traceId": "e9de6b0f5aa10863"}, {"name": "optimize", "duration": 41286, "timestamp": 308226072679, "id": 114, "parentId": 112, "tags": {}, "startTime": 1750158407973, "traceId": "e9de6b0f5aa10863"}, {"name": "module-hash", "duration": 19873, "timestamp": 308226129442, "id": 120, "parentId": 112, "tags": {}, "startTime": 1750158408029, "traceId": "e9de6b0f5aa10863"}, {"name": "code-generation", "duration": 4176, "timestamp": 308226149396, "id": 121, "parentId": 112, "tags": {}, "startTime": 1750158408049, "traceId": "e9de6b0f5aa10863"}]