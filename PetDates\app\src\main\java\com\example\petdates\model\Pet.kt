package com.example.petdates.model

data class Pet(
    val id: String,
    val name: String,
    val breed: String,
    val age: Int,
    val gender: String,
    val description: String,
    val imageUrl: String,
    val location: String,
    val owner: String,
    val isLiked: Boolean = false,
    val isDisliked: Boolean = false,
    val tags: List<String> = emptyList()
) {
    val ageText: String
        get() = if (age == 1) "$age año" else "$age años"
    
    val displayInfo: String
        get() = "$name, $ageText"
}
