package com.example.petdates.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Favorite
import androidx.compose.material.icons.filled.Person
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.petdates.ui.components.SwipeableCard
import com.example.petdates.viewmodel.PetDatesViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SwipeScreen(
    onNavigateToMatches: () -> Unit,
    viewModel: PetDatesViewModel = viewModel()
) {
    val currentPet = viewModel.currentPet
    val newMatchesCount = viewModel.getNewMatchesCount()
    
    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // Top Bar
        TopAppBar(
            title = {
                Text(
                    text = "PetDates",
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.primary
                )
            },
            actions = {
                // Botón de matches con badge
                Box {
                    IconButton(onClick = onNavigateToMatches) {
                        Icon(
                            Icons.Default.Person,
                            contentDescription = "Ver matches",
                            tint = MaterialTheme.colorScheme.primary
                        )
                    }
                    if (newMatchesCount > 0) {
                        Badge(
                            modifier = Modifier.align(Alignment.TopEnd)
                        ) {
                            Text(
                                text = newMatchesCount.toString(),
                                fontSize = 10.sp
                            )
                        }
                    }
                }
            }
        )
        
        // Contenido principal
        Box(
            modifier = Modifier
                .fillMaxSize()
                .weight(1f)
        ) {
            if (currentPet != null) {
                SwipeableCard(
                    pet = currentPet,
                    onSwipeLeft = { viewModel.dislikePet() },
                    onSwipeRight = { viewModel.likePet() }
                )
            } else {
                // No hay más mascotas
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(32.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    Text(
                        text = "🐕",
                        fontSize = 64.sp
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = "¡No hay más mascotas por ahora!",
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold,
                        textAlign = TextAlign.Center
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "Vuelve más tarde para conocer nuevos amigos peludos",
                        fontSize = 16.sp,
                        textAlign = TextAlign.Center,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                    )
                }
            }
        }
        
        // Botones de acción
        if (currentPet != null) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(32.dp),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                // Botón de No Me Gusta
                FloatingActionButton(
                    onClick = { viewModel.dislikePet() },
                    containerColor = Color.Red,
                    contentColor = Color.White,
                    modifier = Modifier.size(56.dp),
                    shape = CircleShape
                ) {
                    Icon(
                        Icons.Default.Close,
                        contentDescription = "No me gusta",
                        modifier = Modifier.size(24.dp)
                    )
                }
                
                // Botón de Me Gusta
                FloatingActionButton(
                    onClick = { viewModel.likePet() },
                    containerColor = Color.Green,
                    contentColor = Color.White,
                    modifier = Modifier.size(56.dp),
                    shape = CircleShape
                ) {
                    Icon(
                        Icons.Default.Favorite,
                        contentDescription = "Me gusta",
                        modifier = Modifier.size(24.dp)
                    )
                }
            }
        }
    }
}
