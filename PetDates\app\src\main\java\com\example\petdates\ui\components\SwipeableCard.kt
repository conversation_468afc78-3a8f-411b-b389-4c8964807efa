package com.example.petdates.ui.components

import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.tween
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.petdates.model.Pet
import kotlinx.coroutines.launch
import kotlin.math.abs

@Composable
fun SwipeableCard(
    pet: Pet,
    onSwipeLeft: () -> Unit,
    onSwipeRight: () -> Unit,
    modifier: Modifier = Modifier
) {
    val configuration = LocalConfiguration.current
    val screenWidth = configuration.screenWidthDp.dp
    val swipeThreshold = screenWidth * 0.3f
    
    val offsetX = remember { Animatable(0f) }
    val offsetY = remember { Animatable(0f) }
    val rotation = remember { Animatable(0f) }
    val scope = rememberCoroutineScope()
    
    val swipeProgress = (abs(offsetX.value) / swipeThreshold.value).coerceIn(0f, 1f)
    val isSwipingRight = offsetX.value > 0
    
    Box(
        modifier = modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // Indicadores de Like/Nope
        if (swipeProgress > 0.3f) {
            Box(
                modifier = Modifier
                    .align(if (isSwipingRight) Alignment.TopEnd else Alignment.TopStart)
                    .padding(32.dp)
            ) {
                Surface(
                    shape = androidx.compose.foundation.shape.RoundedCornerShape(8.dp),
                    color = if (isSwipingRight) Color.Green else Color.Red,
                    modifier = Modifier
                        .graphicsLayer {
                            alpha = swipeProgress
                            scaleX = 0.8f + (swipeProgress * 0.2f)
                            scaleY = 0.8f + (swipeProgress * 0.2f)
                        }
                ) {
                    Text(
                        text = if (isSwipingRight) "LIKE" else "NOPE",
                        modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp),
                        color = Color.White,
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold
                    )
                }
            }
        }
        
        // Tarjeta de la mascota
        PetCard(
            pet = pet,
            modifier = Modifier
                .graphicsLayer {
                    translationX = offsetX.value
                    translationY = offsetY.value
                    rotationZ = rotation.value
                    alpha = 1f - (abs(offsetX.value) / (screenWidth.value * 2))
                }
                .pointerInput(Unit) {
                    detectDragGestures(
                        onDragEnd = {
                            scope.launch {
                                if (abs(offsetX.value) > swipeThreshold.value) {
                                    // Completar el swipe
                                    val targetX = if (offsetX.value > 0) screenWidth.value * 2 else -screenWidth.value * 2
                                    val targetRotation = if (offsetX.value > 0) 30f else -30f
                                    
                                    launch {
                                        offsetX.animateTo(
                                            targetValue = targetX,
                                            animationSpec = tween(300)
                                        )
                                    }
                                    launch {
                                        rotation.animateTo(
                                            targetValue = targetRotation,
                                            animationSpec = tween(300)
                                        )
                                    }
                                    
                                    // Ejecutar callback después de la animación
                                    if (offsetX.value > 0) {
                                        onSwipeRight()
                                    } else {
                                        onSwipeLeft()
                                    }
                                } else {
                                    // Volver a la posición original
                                    launch {
                                        offsetX.animateTo(0f, animationSpec = tween(300))
                                    }
                                    launch {
                                        offsetY.animateTo(0f, animationSpec = tween(300))
                                    }
                                    launch {
                                        rotation.animateTo(0f, animationSpec = tween(300))
                                    }
                                }
                            }
                        }
                    ) { change, _ ->
                        scope.launch {
                            offsetX.snapTo(offsetX.value + change.x)
                            offsetY.snapTo(offsetY.value + change.y)
                            rotation.snapTo(offsetX.value * 0.1f)
                        }
                    }
                }
        )
    }
}
