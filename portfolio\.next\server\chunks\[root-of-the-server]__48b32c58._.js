module.exports = {

"[project]/.next-internal/server/app/favicon.ico/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/app/favicon--route-entry.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "dynamic": (()=>dynamic)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-rsc] (ecmascript)");
;
const contentType = "image/x-icon";
const cacheControl = "public, max-age=0, must-revalidate";
const buffer = Buffer.from("AAABAAEAEBAAAAEAIABoBAAAFgAAACgAAAAQAAAAIAAAAAEAIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD//////v7+//z8/P/09PT/8fHx//b29v/7/Pz//f39//79/f/8/Pz/9/f3//Hy8v/09PT//Pz8//7+/v///////v7+//v7+//z8/P/9fX1//z7+//79/f/9e3r//Hm5P/k3OD/3djf//Tv7v/7+/v/9vb2//Pz8//7+/v//v7+//z8/P/z8/P/9/f3//v5+f/v5OP/7d3a/+va1//u3tr/xb3L/8K2vv/Vwr7/49fV//r39//3+Pj/8/Pz//z8/P/19fX/9fX1//n29v/n2NX/5NHP/+nY1v/s39//2M7a/7Kvyv+UmLH/vK+x/8u4tv/Owb//9PLy//b29v/19fX/8fHx//v7+//k2NX/0r+4/9nFwP/h0c//0cna/8jA2f+4s9H/eYar/6iiqf/Dsaz/tqei/8/Hxf/6+vr/8vLy//X29v/28vH/1cO+/9K/uP/UwLn/0L+7/8C4zv++udf/trPW/4uUwP+loa7/ybWw/7+uqf+8rqr/7+zr//b29v/6+/v/7OPh/9G9uP/Sv7n/1MG6/9TBvP+9tc7/ubbd/6mp2P96hcT/f4qv/8W0sf/Esq3/u6uo/9/Z1//7+/v//Pz9/+bb2P/Rvbf/0r+5/9TAuv/EuMf/sa7W/7Oy4P+lqN//d4XB/2t9sf+9r7H/xrSv/76tqf/Z0M7//Pz8//z8/P/n3Nn/0Ly3/9K+uP/Ovbv/rarT/6ur1/+hoM7/o6PV/3SBuf9icaP/ua2w/8i1sP/Ar6v/29LQ//z8/P/6+vr/7eXj/9C8t//QvLf/zru3/7Gpv/+lo83/dnmk/4OHtP9+hbT/UVt//7Gkpv/JtbD/wrCt/+Xe3f/6+/v/9PX1//f08//Vw7//z7u2/8Cuqv91dYj/oaDJ/6Siyv+dn8v/lZvN/2x5pf+xpaj/yLSw/8q6t//08fH/9fX1//Ly8v/7+/v/593b/8+7tv+5p6T/MzU//1VcgP+Fibn/a3Sl/1tmmP9TX4b/qp2f/8m2sv/i2Nb/+/v7//Ly8v/39/f/9PT0//v5+f/g09D/yrWy/3pwc/8oLT7/GB80/w0SIf8LEB3/QkNN/7qpqP/d0c7/+vj4//T09P/39/f//f39//X19f/19fX/+/r5/+fe3P/Qvrz/mY2Q/11YXv9EQkf/aGNn/6+kpf/l3Nr/+vn5//b29v/19fX//f39//7+/v/9/f3/9fX1//T09P/7+/v/+PX1/+/o5//l3Nv/4NjY/+3m5f/49vX/+/v7//T09P/19fX//Pz8//7+/v///////v7+//39/f/39/f/8vLy//T09P/4+fn/+/v7//v8/P/5+fn/9PT0//Ly8v/39/f//f39//7+/v//////AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", 'base64');
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
}
function GET() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["NextResponse"](buffer, {
        headers: {
            'Content-Type': contentType,
            'Cache-Control': cacheControl
        }
    });
}
const dynamic = 'force-static';
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__48b32c58._.js.map